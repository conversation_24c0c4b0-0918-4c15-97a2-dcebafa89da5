<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研数据地图标注工具</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 120px);
        }
        
        .sidebar {
            width: 350px;
            background: white;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .control-group button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            background: #2980b9;
        }
        
        .control-group button.active {
            background: #e74c3c;
        }
        
        .data-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        
        .data-info h3 {
            margin-top: 0;
            color: #27ae60;
        }
        
        .point-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        .point-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            font-family: monospace;
            font-size: 12px;
        }
        
        .point-item:hover {
            background: #f0f8ff;
        }
        
        .point-item.selected {
            background: #3498db;
            color: white;
        }
        
        .export-section {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .export-section h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .distance-info {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ 科研数据地图标注工具</h1>
        <p>专业级GPS坐标可视化与分析平台</p>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="controls">
                <h3>📊 数据控制</h3>
                <div class="control-group">
                    <label>显示选项:</label>
                    <button id="togglePoints" class="active">显示点位</button>
                    <button id="toggleLabels" class="active">显示标签</button>
                    <button id="toggleLines">连接线</button>
                </div>
                <div class="control-group">
                    <label>地图样式:</label>
                    <button onclick="changeMapStyle('satellite')">卫星图</button>
                    <button onclick="changeMapStyle('street')">街道图</button>
                    <button onclick="changeMapStyle('terrain')">地形图</button>
                </div>
                <div class="control-group">
                    <button onclick="fitAllPoints()">🎯 适应所有点位</button>
                    <button onclick="exportData()">📄 导出数据</button>
                </div>
            </div>
            
            <div class="data-info">
                <h3>📈 数据统计</h3>
                <div id="dataStats">
                    <p>总点位数: <span id="totalPoints">0</span></p>
                    <p>经度范围: <span id="lngRange">-</span></p>
                    <p>纬度范围: <span id="latRange">-</span></p>
                    <p>覆盖面积: <span id="coverageArea">-</span></p>
                </div>
            </div>
            
            <div class="control-group">
                <label>📍 点位列表:</label>
                <div class="point-list" id="pointList"></div>
            </div>
            
            <div id="distanceInfo" class="distance-info" style="display:none;">
                <strong>距离信息:</strong>
                <div id="distanceDetails"></div>
            </div>
            
            <div class="export-section">
                <h3>📋 导出选项</h3>
                <button onclick="exportCSV()">导出CSV</button>
                <button onclick="exportKML()">导出KML</button>
                <button onclick="exportImage()">导出图片</button>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 您的GPS数据
        const gpsData = [
            {id: 1, lat: 24.537222, lng: 118.290556, label: "15.6"},
            {id: 2, lat: 24.534722, lng: 118.290556, label: "12.3"},
            {id: 3, lat: 24.538056, lng: 118.290556, label: "16.8"},
            {id: 4, lat: 24.538611, lng: 118.290556, label: "17.7"},
            {id: 5, lat: 24.538944, lng: 118.290556, label: "19.1"},
            {id: 6, lat: 24.538194, lng: 118.288056, label: "47.5"},
            {id: 7, lat: 24.537500, lng: 118.285556, label: "24"},
            {id: 8, lat: 24.538333, lng: 118.288611, label: "17.8"},
            {id: 9, lat: 24.535833, lng: 118.287917, label: "33"},
            {id: 10, lat: 24.535194, lng: 118.287778, label: "29"},
            {id: 11, lat: 24.537222, lng: 118.287361, label: "32"},
            {id: 12, lat: 24.537833, lng: 118.287361, label: "17.6"},
            {id: 13, lat: 24.537972, lng: 118.287361, label: "21.6"},
            {id: 14, lat: 24.534083, lng: 118.287500, label: "34.2"},
            {id: 15, lat: 24.537500, lng: 118.288056, label: "16"},
            {id: 16, lat: 24.533889, lng: 118.287722, label: "60.8"},
            {id: 17, lat: 24.536833, lng: 118.287722, label: "50"},
            {id: 18, lat: 24.540833, lng: 118.287722, label: "20"},
            {id: 19, lat: 24.542361, lng: 118.287917, label: "24"},
            {id: 20, lat: 24.540556, lng: 118.287361, label: "20"},
            {id: 21, lat: 24.537639, lng: 118.287500, label: "40"},
            {id: 22, lat: 24.533889, lng: 118.287222, label: "24.5"},
            {id: 23, lat: 24.538333, lng: 118.287444, label: "19"},
            {id: 24, lat: 24.539167, lng: 118.287444, label: "19.4"},
            {id: 25, lat: 24.533750, lng: 118.287917, label: "60"},
            {id: 26, lat: 24.535278, lng: 118.286944, label: "65"},
            {id: 27, lat: 24.535833, lng: 118.286944, label: "52"},
            {id: 28, lat: 24.535833, lng: 118.287222, label: "40"},
            {id: 29, lat: 24.534722, lng: 118.287222, label: "87"},
            {id: 30, lat: 24.536250, lng: 118.288611, label: "45"},
            {id: 31, lat: 24.536389, lng: 118.288611, label: "17.3"},
            {id: 32, lat: 24.536389, lng: 118.288611, label: "18"},
            {id: 33, lat: 24.536389, lng: 118.288611, label: "15.6"},
            {id: 34, lat: 24.536389, lng: 118.290556, label: "18.6"}
        ];

        // 初始化地图
        let map = L.map('map').setView([24.537, 118.288], 16);
        
        // 地图图层
        const mapLayers = {
            street: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }),
            satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri'
            }),
            terrain: L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenTopoMap contributors'
            })
        };
        
        mapLayers.street.addTo(map);
        
        // 存储标记和线条
        let markers = [];
        let polyline = null;
        let selectedMarker = null;
        
        // 初始化数据
        initializeMap();
        updateStatistics();
        createPointList();
        
        function initializeMap() {
            // 清除现有标记
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];
            
            // 添加标记
            gpsData.forEach((point, index) => {
                const marker = L.marker([point.lat, point.lng])
                    .bindPopup(`
                        <strong>点位 ${point.id}</strong><br>
                        数值: ${point.label}<br>
                        坐标: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)}
                    `)
                    .on('click', () => selectPoint(index));
                
                markers.push(marker);
                marker.addTo(map);
            });
        }
        
        function updateStatistics() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            
            document.getElementById('totalPoints').textContent = gpsData.length;
            document.getElementById('lngRange').textContent = 
                `${Math.min(...lngs).toFixed(6)} ~ ${Math.max(...lngs).toFixed(6)}`;
            document.getElementById('latRange').textContent = 
                `${Math.min(...lats).toFixed(6)} ~ ${Math.max(...lats).toFixed(6)}`;
            
            // 计算覆盖面积（粗略估算）
            const latRange = Math.max(...lats) - Math.min(...lats);
            const lngRange = Math.max(...lngs) - Math.min(...lngs);
            const area = latRange * lngRange * 111000 * 111000; // 转换为平方米
            document.getElementById('coverageArea').textContent = 
                area > 1000000 ? `${(area/1000000).toFixed(2)} km²` : `${area.toFixed(0)} m²`;
        }
        
        function createPointList() {
            const pointList = document.getElementById('pointList');
            pointList.innerHTML = '';
            
            gpsData.forEach((point, index) => {
                const div = document.createElement('div');
                div.className = 'point-item';
                div.innerHTML = `${point.id}: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)} (${point.label})`;
                div.onclick = () => selectPoint(index);
                pointList.appendChild(div);
            });
        }
        
        function selectPoint(index) {
            // 更新选中状态
            document.querySelectorAll('.point-item').forEach((item, i) => {
                item.classList.toggle('selected', i === index);
            });
            
            // 高亮标记
            if (selectedMarker) {
                selectedMarker.setIcon(new L.Icon.Default());
            }
            
            selectedMarker = markers[index];
            selectedMarker.setIcon(L.icon({
                iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34],
                shadowSize: [41, 41]
            }));
            
            // 移动地图到选中点
            map.setView([gpsData[index].lat, gpsData[index].lng], 18);
            
            // 显示距离信息
            showDistanceInfo(index);
        }
        
        function showDistanceInfo(index) {
            const distanceInfo = document.getElementById('distanceInfo');
            const distanceDetails = document.getElementById('distanceDetails');
            
            if (index === 0) {
                distanceInfo.style.display = 'none';
                return;
            }
            
            const currentPoint = gpsData[index];
            const prevPoint = gpsData[index - 1];
            
            const distance = calculateDistance(
                prevPoint.lat, prevPoint.lng,
                currentPoint.lat, currentPoint.lng
            );
            
            distanceDetails.innerHTML = `
                到前一点距离: ${distance.toFixed(2)} 米<br>
                坐标差异:<br>
                - 纬度: ${(currentPoint.lat - prevPoint.lat).toFixed(8)}°<br>
                - 经度: ${(currentPoint.lng - prevPoint.lng).toFixed(8)}°
            `;
            
            distanceInfo.style.display = 'block';
        }
        
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371000; // 地球半径（米）
            const φ1 = lat1 * Math.PI / 180;
            const φ2 = lat2 * Math.PI / 180;
            const Δφ = (lat2 - lat1) * Math.PI / 180;
            const Δλ = (lng2 - lng1) * Math.PI / 180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }
        
        // 控制按钮功能
        document.getElementById('togglePoints').onclick = function() {
            this.classList.toggle('active');
            const show = this.classList.contains('active');
            markers.forEach(marker => {
                if (show) {
                    marker.addTo(map);
                } else {
                    map.removeLayer(marker);
                }
            });
        };
        
        document.getElementById('toggleLines').onclick = function() {
            this.classList.toggle('active');
            const show = this.classList.contains('active');
            
            if (show && !polyline) {
                const latlngs = gpsData.map(point => [point.lat, point.lng]);
                polyline = L.polyline(latlngs, {color: 'red', weight: 2}).addTo(map);
            } else if (!show && polyline) {
                map.removeLayer(polyline);
                polyline = null;
            }
        };
        
        function changeMapStyle(style) {
            map.eachLayer(layer => {
                if (layer instanceof L.TileLayer) {
                    map.removeLayer(layer);
                }
            });
            mapLayers[style].addTo(map);
        }
        
        function fitAllPoints() {
            const group = new L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
        }
        
        function exportCSV() {
            let csv = 'ID,纬度,经度,数值\n';
            gpsData.forEach(point => {
                csv += `${point.id},${point.lat},${point.lng},${point.label}\n`;
            });
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'research_data.csv';
            a.click();
        }
        
        function exportData() {
            alert('数据导出功能：\n1. CSV格式 - 表格数据\n2. KML格式 - Google Earth\n3. 图片格式 - 地图截图');
        }
        
        function exportKML() {
            alert('KML导出功能开发中...');
        }
        
        function exportImage() {
            alert('图片导出功能开发中...');
        }
    </script>
</body>
</html>
