<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS Points Relative Position Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .header h1 {
            font-size: 2.8em;
            font-weight: bold;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 0;
            min-height: 900px;
        }

        .sidebar {
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-right: 2px solid #dee2e6;
        }

        .control-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .control-section h3 {
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        .control-group {
            margin-bottom: 18px;
        }

        .control-group label {
            display: block;
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .control-group select,
        .control-group input[type="range"] {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            background: white;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .btn {
            width: 100%;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-weight: bold;
            font-size: 15px;
            font-family: Arial, sans-serif;
            cursor: pointer;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        .plot-area {
            padding: 40px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        #plotCanvas {
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            border: 3px solid #ffffff;
            background: white;
        }

        .stats-panel {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #c3e6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.7);
        }

        .stat-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #155724;
            display: block;
        }

        .stat-label {
            font-size: 0.85em;
            color: #155724;
            opacity: 0.8;
            margin-top: 5px;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            pointer-events: none;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📍 GPS Turbidity Sampling Points Analysis</h1>
            <p>High-Precision Spatial Distribution with Anti-Overlap Algorithm</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="control-section">
                    <h3>🎨 Visual Settings</h3>
                    <div class="control-group">
                        <label>Canvas Size</label>
                        <select id="canvasSize" onchange="updateCanvas()">
                            <option value="1400,1000">1400×1000 (Standard)</option>
                            <option value="1800,1300">1800×1300 (Large)</option>
                            <option value="2400,1700">2400×1700 (High Quality)</option>
                            <option value="3000,2100">3000×2100 (Ultra HD)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Point Size</label>
                        <input type="range" id="pointSize" min="8" max="30" value="15" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>8px</span>
                            <span id="pointSizeValue">15px</span>
                            <span>30px</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Anti-Overlap Strength</label>
                        <input type="range" id="overlapStrength" min="1" max="10" value="5" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>Weak</span>
                            <span id="overlapValue">Medium</span>
                            <span>Strong</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Label Font Size</label>
                        <input type="range" id="fontSize" min="10" max="20" value="12" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>10px</span>
                            <span id="fontSizeValue">12px</span>
                            <span>20px</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>📊 Display Options</h3>
                    <div class="control-group">
                        <label>Label Content</label>
                        <select id="labelStyle" onchange="redrawPlot()">
                            <option value="id">Point ID Only</option>
                            <option value="value" selected>Turbidity Value</option>
                            <option value="both">ID + Turbidity</option>
                            <option value="unit">Turbidity + Unit</option>
                            <option value="none">No Labels</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showGrid" checked onchange="redrawPlot()">
                            <label>Show Grid</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showAxes" checked onchange="redrawPlot()">
                            <label>Show Coordinate Axes</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showConnections" onchange="redrawPlot()">
                            <label>Show Connection Lines</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showOriginal" onchange="redrawPlot()">
                            <label>Show Original Positions</label>
                        </div>
                    </div>
                </div>

                <div class="stats-panel">
                    <h3>📊 Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value" id="totalPoints">-</span>
                            <div class="stat-label">Total Points</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="areaSize">-</span>
                            <div class="stat-label">Coverage Area</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="avgDistance">-</span>
                            <div class="stat-label">Avg Distance</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="adjustedPoints">-</span>
                            <div class="stat-label">Adjusted Points</div>
                        </div>
                    </div>
                </div>

                <button class="btn btn-primary" onclick="generatePlot()">
                    🖼️ Generate High-Quality Plot
                </button>
            </div>

            <div class="plot-area">
                <canvas id="plotCanvas"></canvas>
            </div>
        </div>
    </div>

    <div class="loading" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold;">Generating Plot...</p>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Complete GPS data from your image
        const gpsData = [
            {id: 1, value: 15.6, lat: 24 + 32/60 + 18.37/3600, lng: 118 + 17/60 + 24.60/3600},
            {id: 2, value: 12.3, lat: 24 + 32/60 + 18.26/3600, lng: 118 + 17/60 + 24.45/3600},
            {id: 3, value: 16.8, lat: 24 + 32/60 + 18.16/3600, lng: 118 + 17/60 + 24.36/3600},
            {id: 4, value: 17.7, lat: 24 + 32/60 + 18.09/3600, lng: 118 + 17/60 + 24.03/3600},
            {id: 5, value: 19.1, lat: 24 + 32/60 + 18.32/3600, lng: 118 + 17/60 + 23.88/3600},
            {id: 6, value: 47.5, lat: 24 + 32/60 + 18.30/3600, lng: 118 + 17/60 + 18.20/3600},
            {id: 7, value: 24, lat: 24 + 32/60 + 15.20/3600, lng: 118 + 17/60 + 14.04/3600},
            {id: 8, value: 17.8, lat: 24 + 32/60 + 12.90/3600, lng: 118 + 17/60 + 18.86/3600},
            {id: 9, value: 33, lat: 24 + 32/60 + 8.21/3600, lng: 118 + 17/60 + 17.25/3600},
            {id: 10, value: 29, lat: 24 + 32/60 + 10.47/3600, lng: 118 + 17/60 + 15.24/3600},
            {id: 11, value: 32, lat: 24 + 32/60 + 12.42/3600, lng: 118 + 17/60 + 14.45/3600},
            {id: 12, value: 17.6, lat: 24 + 32/60 + 14.04/3600, lng: 118 + 17/60 + 14.51/3600},
            {id: 13, value: 21.6, lat: 24 + 32/60 + 14.83/3600, lng: 118 + 17/60 + 14.54/3600},
            {id: 14, value: 34.2, lat: 24 + 32/60 + 16.27/3600, lng: 118 + 17/60 + 15.04/3600},
            {id: 15, value: 16, lat: 24 + 32/60 + 17.50/3600, lng: 118 + 17/60 + 15.89/3600},
            {id: 16, value: 60.8, lat: 24 + 32/60 + 20.30/3600, lng: 118 + 17/60 + 16.18/3600},
            {id: 17, value: 50, lat: 24 + 32/60 + 21.64/3600, lng: 118 + 17/60 + 16.19/3600},
            {id: 18, value: 20, lat: 24 + 32/60 + 24.50/3600, lng: 118 + 17/60 + 16.36/3600},
            {id: 19, value: 24, lat: 24 + 32/60 + 25.74/3600, lng: 118 + 17/60 + 15.70/3600},
            {id: 20, value: 20, lat: 24 + 32/60 + 24.34/3600, lng: 118 + 17/60 + 14.33/3600},
            {id: 21, value: 40, lat: 24 + 32/60 + 22.75/3600, lng: 118 + 17/60 + 13.94/3600},
            {id: 22, value: 24.5, lat: 24 + 32/60 + 20.30/3600, lng: 118 + 17/60 + 13.30/3600},
            {id: 23, value: 19, lat: 24 + 32/60 + 17.30/3600, lng: 118 + 17/60 + 12.96/3600},
            {id: 24, value: 19.4, lat: 24 + 32/60 + 13.34/3600, lng: 118 + 17/60 + 12.58/3600},
            {id: 25, value: 60, lat: 24 + 32/60 + 20.25/3600, lng: 118 + 17/60 + 17.49/3600},
            {id: 26, value: 65, lat: 24 + 32/60 + 21.11/3600, lng: 118 + 17/60 + 17.15/3600},
            {id: 27, value: 52, lat: 24 + 32/60 + 21.50/3600, lng: 118 + 17/60 + 17.29/3600},
            {id: 28, value: 40, lat: 24 + 32/60 + 21.03/3600, lng: 118 + 17/60 + 17.54/3600},
            {id: 29, value: 87, lat: 24 + 32/60 + 20.86/3600, lng: 118 + 17/60 + 17.40/3600},
            {id: 30, value: 45, lat: 24 + 32/60 + 21.15/3600, lng: 118 + 17/60 + 20.31/3600},
            {id: 31, value: 17.3, lat: 24 + 32/60 + 21.52/3600, lng: 118 + 17/60 + 21.05/3600},
            {id: 32, value: 18, lat: 24 + 32/60 + 21.78/3600, lng: 118 + 17/60 + 21.05/3600},
            {id: 33, value: 15.6, lat: 24 + 32/60 + 21.80/3600, lng: 118 + 17/60 + 21.10/3600},
            {id: 34, value: 18.6, lat: 24 + 32/60 + 21.59/3600, lng: 118 + 17/60 + 24.09/3600}
        ];

        let canvas, ctx;
        let plotWidth, plotHeight, margin;
        let adjustedPositions = [];

        // Initialize
        window.onload = function() {
            canvas = document.getElementById('plotCanvas');
            ctx = canvas.getContext('2d');

            canvas.addEventListener('mousemove', handleMouseMove);

            updateCanvas();
            calculateStatistics();
        };

        function updateCanvas() {
            const [width, height] = document.getElementById('canvasSize').value.split(',').map(Number);
            canvas.width = width;
            canvas.height = height;
            canvas.style.maxWidth = '100%';
            canvas.style.maxHeight = '800px';

            // Increase margin to accommodate labels
            margin = Math.min(width, height) * 0.18;
            plotWidth = width - 2 * margin;
            plotHeight = height - 2 * margin;

            redrawPlot();
        }

        function redrawPlot() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // High quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // Beautiful background
            drawBackground();

            // Calculate bounds
            const bounds = calculateBounds();

            // Calculate adjusted positions with anti-overlap
            calculateAdjustedPositions(bounds);

            // Draw grid
            if (document.getElementById('showGrid').checked) {
                drawGrid(bounds);
            }

            // Draw axes
            if (document.getElementById('showAxes').checked) {
                drawAxes(bounds);
            }

            // Draw original positions if enabled
            if (document.getElementById('showOriginal').checked) {
                drawOriginalPositions(bounds);
            }

            // Draw connection lines
            if (document.getElementById('showConnections').checked) {
                drawConnections();
            }

            // Draw data points
            drawDataPoints();

            // Draw title
            drawTitle();

            // Update displays
            updateDisplays();
        }

        function drawBackground() {
            const gradient = ctx.createRadialGradient(
                canvas.width/2, canvas.height/2, 0,
                canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2
            );
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(0.8, '#fafbfc');
            gradient.addColorStop(1, '#f1f3f4');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function calculateBounds() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const minLat = Math.min(...lats);
            const maxLat = Math.max(...lats);
            const minLng = Math.min(...lngs);
            const maxLng = Math.max(...lngs);

            const latRange = maxLat - minLat;
            const lngRange = maxLng - minLng;
            const padding = Math.max(latRange, lngRange) * 0.15;

            return {
                minLat: minLat - padding,
                maxLat: maxLat + padding,
                minLng: minLng - padding,
                maxLng: maxLng + padding,
                latRange: latRange,
                lngRange: lngRange
            };
        }

        function calculateAdjustedPositions(bounds) {
            const overlapStrength = parseInt(document.getElementById('overlapStrength').value);
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const minDistance = pointSize * (1 + overlapStrength * 0.3);

            adjustedPositions = [];

            // Calculate original positions
            gpsData.forEach((point, index) => {
                const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                adjustedPositions.push({
                    originalX: x,
                    originalY: y,
                    x: x,
                    y: y,
                    point: point,
                    index: index,
                    moved: false
                });
            });

            // Anti-overlap algorithm with multiple iterations
            for (let iteration = 0; iteration < 100; iteration++) {
                let hasOverlap = false;

                for (let i = 0; i < adjustedPositions.length; i++) {
                    for (let j = i + 1; j < adjustedPositions.length; j++) {
                        const pos1 = adjustedPositions[i];
                        const pos2 = adjustedPositions[j];

                        const dx = pos2.x - pos1.x;
                        const dy = pos2.y - pos1.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < minDistance && distance > 0) {
                            hasOverlap = true;

                            // Calculate repulsion force
                            const overlap = minDistance - distance;
                            const force = overlap / distance * 0.6;
                            const forceX = dx * force;
                            const forceY = dy * force;

                            // Apply force
                            pos1.x -= forceX;
                            pos1.y -= forceY;
                            pos2.x += forceX;
                            pos2.y += forceY;

                            // Mark as moved
                            pos1.moved = Math.abs(pos1.x - pos1.originalX) > 3 || Math.abs(pos1.y - pos1.originalY) > 3;
                            pos2.moved = Math.abs(pos2.x - pos2.originalX) > 3 || Math.abs(pos2.y - pos2.originalY) > 3;

                            // Keep within bounds
                            pos1.x = Math.max(margin + pointSize, Math.min(margin + plotWidth - pointSize, pos1.x));
                            pos1.y = Math.max(margin + pointSize, Math.min(margin + plotHeight - pointSize, pos1.y));
                            pos2.x = Math.max(margin + pointSize, Math.min(margin + plotWidth - pointSize, pos2.x));
                            pos2.y = Math.max(margin + pointSize, Math.min(margin + plotHeight - pointSize, pos2.y));
                        }
                    }
                }

                if (!hasOverlap) break;
            }
        }

        function drawGrid(bounds) {
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.06)';
            ctx.lineWidth = 1;

            const gridLines = 15;
            for (let i = 0; i <= gridLines; i++) {
                // Vertical lines
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / gridLines;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();

                // Horizontal lines
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / gridLines;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }
        }

        function drawAxes(bounds) {
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.font = 'bold 14px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';

            // Draw axes
            ctx.beginPath();
            ctx.moveTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.stroke();

            // X-axis labels (Longitude)
            const xTicks = 5;
            ctx.textAlign = 'center';
            for (let i = 0; i <= xTicks; i++) {
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / xTicks;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, margin + plotHeight);
                ctx.lineTo(x, margin + plotHeight + 10);
                ctx.stroke();

                ctx.font = '20px Arial, sans-serif';
                const label = lng.toFixed(5) + '°';
                ctx.fillText(label, x, margin + plotHeight + 30);
            }

            // Y-axis labels (Latitude)
            const yTicks = 5;
            ctx.textAlign = 'right';
            for (let i = 0; i <= yTicks; i++) {
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / yTicks;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(margin - 10, y);
                ctx.lineTo(margin, y);
                ctx.stroke();

                ctx.font = '20px Arial, sans-serif';
                const label = lat.toFixed(5) + '°';
                ctx.fillText(label, margin - 20, y + 5);
            }

            // Axis titles
            ctx.font = 'bold 16px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Longitude (°)', margin + plotWidth/2, canvas.height - 20);

            ctx.save();
            ctx.translate(30, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.textAlign = 'center';
            ctx.fillText('Latitude (°)', 0, 0);
            ctx.restore();
        }

        function drawOriginalPositions(bounds) {
            adjustedPositions.forEach(pos => {
                if (pos.moved) {
                    // Draw original position as small gray circle
                    ctx.fillStyle = 'rgba(150, 150, 150, 0.5)';
                    ctx.beginPath();
                    ctx.arc(pos.originalX, pos.originalY, 4, 0, 2 * Math.PI);
                    ctx.fill();

                    // Draw connection line
                    ctx.strokeStyle = 'rgba(150, 150, 150, 0.3)';
                    ctx.lineWidth = 1;
                    ctx.setLineDash([2, 2]);
                    ctx.beginPath();
                    ctx.moveTo(pos.originalX, pos.originalY);
                    ctx.lineTo(pos.x, pos.y);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
            });
        }

        function drawConnections() {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.2)';
            ctx.lineWidth = 1;

            for (let i = 0; i < adjustedPositions.length - 1; i++) {
                const pos1 = adjustedPositions[i];
                const pos2 = adjustedPositions[i + 1];

                ctx.beginPath();
                ctx.moveTo(pos1.x, pos1.y);
                ctx.lineTo(pos2.x, pos2.y);
                ctx.stroke();
            }
        }

        function drawDataPoints() {
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const labelStyle = document.getElementById('labelStyle').value;
            const fontSize = parseInt(document.getElementById('fontSize').value);

            adjustedPositions.forEach(pos => {
                const point = pos.point;
                const x = pos.x;
                const y = pos.y;

                // Get color based on value
                const color = getPointColor(point.value);

                // Draw point with glow effect
                ctx.shadowColor = color;
                ctx.shadowBlur = 8;
                ctx.globalAlpha = 0.4;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize + 3, 0, 2 * Math.PI);
                ctx.fill();

                // Reset shadow
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.globalAlpha = 1;

                // Main point
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
                ctx.fill();

                // Inner highlight
                ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.beginPath();
                ctx.arc(x - pointSize/3, y - pointSize/3, pointSize/4, 0, 2 * Math.PI);
                ctx.fill();

                // Border
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
                ctx.stroke();

                // Draw labels
                if (labelStyle !== 'none') {
                    drawPointLabel(point, x, y, pointSize, labelStyle, fontSize);
                }
            });
        }

        function getPointColor(value) {
            const values = gpsData.map(p => p.value);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);
            const ratio = (value - minVal) / (maxVal - minVal);

            // Blue to red gradient based on value
            if (ratio < 0.33) return '#3498db';      // Blue for low values
            else if (ratio < 0.66) return '#f39c12'; // Orange for medium values
            else return '#e74c3c';                   // Red for high values
        }

        function drawPointLabel(point, x, y, pointSize, labelStyle, fontSize) {
            ctx.font = `bold ${fontSize}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let label = '';
            switch (labelStyle) {
                case 'id':
                    label = point.id.toString();
                    break;
                case 'value':
                    label = point.value.toString();
                    break;
                case 'both':
                    label = `${point.id}: ${point.value}`;
                    break;
                case 'unit':
                    label = `${point.value} NTU`;
                    break;
            }

            // Calculate label position to avoid overlap
            const textWidth = ctx.measureText(label).width;
            const textHeight = fontSize;
            const padding = 4;

            // Try different positions around the point
            const positions = [
                {x: x, y: y - pointSize - textHeight/2 - 8},     // Top
                {x: x, y: y + pointSize + textHeight/2 + 8},     // Bottom
                {x: x - textWidth/2 - pointSize - 8, y: y},      // Left
                {x: x + textWidth/2 + pointSize + 8, y: y},      // Right
            ];

            let bestPos = positions[0];
            let minOverlap = Infinity;

            // Find position with least overlap
            positions.forEach(pos => {
                let overlap = 0;
                adjustedPositions.forEach(otherPos => {
                    if (otherPos.point.id !== point.id) {
                        const dist = Math.sqrt((pos.x - otherPos.x) ** 2 + (pos.y - otherPos.y) ** 2);
                        if (dist < textWidth + pointSize) overlap++;
                    }
                });
                if (overlap < minOverlap) {
                    minOverlap = overlap;
                    bestPos = pos;
                }
            });

            // Draw label background
            const labelX = bestPos.x;
            const labelY = bestPos.y;

            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillRect(
                labelX - textWidth/2 - padding,
                labelY - textHeight/2 - padding,
                textWidth + padding * 2,
                textHeight + padding * 2
            );

            // Draw label border
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.lineWidth = 1;
            ctx.strokeRect(
                labelX - textWidth/2 - padding,
                labelY - textHeight/2 - padding,
                textWidth + padding * 2,
                textHeight + padding * 2
            );

            // Draw label text
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(label, labelX, labelY);
        }

        function drawTitle() {
            ctx.font = 'bold 24px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText('GPS Turbidity Sampling Points Distribution', canvas.width/2, 50);

            ctx.font = '14px Arial, sans-serif';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText(`${gpsData.length} turbidity sampling points with anti-overlap positioning`, canvas.width/2, 75);

            // Top-right info
            ctx.font = '12px Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillStyle = '#95a5a6';
            const date = new Date().toLocaleDateString('en-US');
            ctx.fillText(`Generated: ${date}`, canvas.width - 30, 30);
            ctx.fillText('Coordinate System: WGS84', canvas.width - 30, 50);
            ctx.fillText('Unit: NTU (Nephelometric Turbidity Units)', canvas.width - 30, 70);
        }

        function updateDisplays() {
            document.getElementById('pointSizeValue').textContent = document.getElementById('pointSize').value + 'px';
            document.getElementById('fontSizeValue').textContent = document.getElementById('fontSize').value + 'px';

            const overlapStrength = parseInt(document.getElementById('overlapStrength').value);
            const overlapLabels = ['Very Weak', 'Weak', 'Light', 'Medium', 'Strong', 'Very Strong', 'Maximum'];
            document.getElementById('overlapValue').textContent = overlapLabels[Math.min(overlapStrength - 1, overlapLabels.length - 1)] || 'Medium';
        }

        function calculateStatistics() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const values = gpsData.map(p => p.value);

            // Calculate area
            const latSpan = (Math.max(...lats) - Math.min(...lats)) * 111000;
            const lngSpan = (Math.max(...lngs) - Math.min(...lngs)) * 111000 * Math.cos(Math.min(...lats) * Math.PI / 180);
            const area = latSpan * lngSpan;

            // Calculate average distance
            let totalDistance = 0;
            let count = 0;
            for (let i = 0; i < gpsData.length; i++) {
                for (let j = i + 1; j < gpsData.length; j++) {
                    totalDistance += calculateDistance(gpsData[i], gpsData[j]);
                    count++;
                }
            }
            const avgDistance = totalDistance / count;

            // Update display
            document.getElementById('totalPoints').textContent = gpsData.length;
            document.getElementById('areaSize').textContent = area > 1000000 ?
                (area/1000000).toFixed(2) + ' km²' : area.toFixed(0) + ' m²';
            document.getElementById('avgDistance').textContent = avgDistance.toFixed(1) + ' m';

            // Count adjusted points (will be updated after positioning)
            setTimeout(() => {
                const adjustedCount = adjustedPositions.filter(pos => pos.moved).length;
                document.getElementById('adjustedPoints').textContent = adjustedCount;
            }, 100);
        }

        function calculateDistance(p1, p2) {
            const R = 6371000;
            const φ1 = p1.lat * Math.PI / 180;
            const φ2 = p2.lat * Math.PI / 180;
            const Δφ = (p2.lat - p1.lat) * Math.PI / 180;
            const Δλ = (p2.lng - p1.lng) * Math.PI / 180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }

        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const x = (event.clientX - rect.left) * scaleX;
            const y = (event.clientY - rect.top) * scaleY;

            const tooltip = document.getElementById('tooltip');
            let found = false;

            adjustedPositions.forEach(pos => {
                const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
                if (distance < 25) {
                    tooltip.style.display = 'block';
                    tooltip.style.left = (event.clientX + 15) + 'px';
                    tooltip.style.top = (event.clientY - 15) + 'px';
                    tooltip.innerHTML = `
                        <strong>Sampling Point ${pos.point.id}</strong><br>
                        Turbidity: ${pos.point.value} NTU<br>
                        Latitude: ${pos.point.lat.toFixed(6)}°<br>
                        Longitude: ${pos.point.lng.toFixed(6)}°<br>
                        ${pos.moved ? '<em>Position adjusted for clarity</em>' : '<em>Original position</em>'}
                    `;
                    found = true;
                }
            });

            if (!found) {
                tooltip.style.display = 'none';
            }
        }

        function generatePlot() {
            document.getElementById('loadingOverlay').style.display = 'flex';

            setTimeout(() => {
                const link = document.createElement('a');
                link.download = 'gps_turbidity_sampling_points_' + new Date().toISOString().slice(0,10) + '.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1000);
        }
    </script>
</body>
</html>
