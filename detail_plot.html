<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS点位细节分布图生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }
        
        .control-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .control-group select, .control-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .control-group button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .control-group button:hover {
            background: #c0392b;
        }
        
        .plot-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        #plotCanvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .info-panel {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-family: monospace;
        }
        
        .color-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 GPS点位细节分布图</h1>
        <p>高精度点位相对位置分析图 - 专为科研论文设计</p>
    </div>
    
    <div class="controls">
        <div class="control-group">
            <label>图片尺寸:</label>
            <select id="canvasSize" onchange="updateCanvas()">
                <option value="800,600">800×600</option>
                <option value="1000,800">1000×800</option>
                <option value="1200,900">1200×900</option>
                <option value="1600,1200">1600×1200</option>
                <option value="2000,1500">2000×1500 (高清)</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>标注方式:</label>
            <select id="labelStyle" onchange="redrawPlot()">
                <option value="numbers">显示编号</option>
                <option value="values">显示数值</option>
                <option value="both">编号+数值</option>
                <option value="none">仅显示点</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>颜色方案:</label>
            <select id="colorScheme" onchange="redrawPlot()">
                <option value="value">按数值着色</option>
                <option value="position">按位置着色</option>
                <option value="single">单一颜色</option>
                <option value="gradient">渐变色</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>点大小:</label>
            <input type="range" id="pointSize" min="3" max="15" value="8" onchange="redrawPlot()">
            <span id="pointSizeValue">8px</span>
        </div>
        
        <div class="control-group">
            <label>网格:</label>
            <input type="checkbox" id="showGrid" checked onchange="redrawPlot()"> 显示网格
            <input type="checkbox" id="showAxes" checked onchange="redrawPlot()"> 显示坐标轴
        </div>
        
        <div class="control-group">
            <button onclick="downloadPlot()">📥 下载细节图</button>
            <button onclick="downloadData()">📊 下载数据</button>
        </div>
    </div>
    
    <div class="plot-container">
        <canvas id="plotCanvas"></canvas>
        
        <div class="info-panel">
            <h3>📈 分布统计信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <strong>总点位数</strong><br>
                    <span id="totalPoints">34</span>
                </div>
                <div class="stat-item">
                    <strong>经度跨度</strong><br>
                    <span id="lngSpan">-</span>
                </div>
                <div class="stat-item">
                    <strong>纬度跨度</strong><br>
                    <span id="latSpan">-</span>
                </div>
                <div class="stat-item">
                    <strong>最大距离</strong><br>
                    <span id="maxDistance">-</span>
                </div>
                <div class="stat-item">
                    <strong>平均间距</strong><br>
                    <span id="avgDistance">-</span>
                </div>
                <div class="stat-item">
                    <strong>数值范围</strong><br>
                    <span id="valueRange">-</span>
                </div>
            </div>
            
            <div class="color-legend" id="colorLegend">
                <!-- 动态生成图例 -->
            </div>
        </div>
    </div>

    <script>
        // GPS数据 - 精确的十进制坐标
        const gpsData = [
            {id: 1, lat: 24.538436, lng: 118.290167, value: 15.6},
            {id: 2, lat: 24.538406, lng: 118.290125, value: 12.3},
            {id: 3, lat: 24.538378, lng: 118.290100, value: 16.8},
            {id: 4, lat: 24.538358, lng: 118.290008, value: 17.7},
            {id: 5, lat: 24.538422, lng: 118.289967, value: 19.1},
            {id: 6, lat: 24.538417, lng: 118.288389, value: 47.5},
            {id: 7, lat: 24.537533, lng: 118.285567, value: 24},
            {id: 8, lat: 24.536917, lng: 118.288572, value: 17.8},
            {id: 9, lat: 24.535728, lng: 118.287569, value: 33},
            {id: 10, lat: 24.535686, lng: 118.287540, value: 29},
            {id: 11, lat: 24.537228, lng: 118.287403, value: 32},
            {id: 12, lat: 24.537398, lng: 118.287364, value: 17.6},
            {id: 13, lat: 24.537440, lng: 118.287373, value: 21.6},
            {id: 14, lat: 24.534508, lng: 118.287501, value: 34.2},
            {id: 15, lat: 24.537639, lng: 118.288052, value: 16},
            {id: 16, lat: 24.533858, lng: 118.287717, value: 60.8},
            {id: 17, lat: 24.536901, lng: 118.287719, value: 50},
            {id: 18, lat: 24.540792, lng: 118.287722, value: 20},
            {id: 19, lat: 24.542373, lng: 118.287917, value: 24},
            {id: 20, lat: 24.540539, lng: 118.287370, value: 20},
            {id: 21, lat: 24.537632, lng: 118.287483, value: 40},
            {id: 22, lat: 24.533858, lng: 118.287258, value: 24.5},
            {id: 23, lat: 24.538314, lng: 118.287460, value: 19},
            {id: 24, lat: 24.539204, lng: 118.287461, value: 19.4},
            {id: 25, lat: 24.533736, lng: 118.287914, value: 60},
            {id: 26, lat: 24.535308, lng: 118.286932, value: 65},
            {id: 27, lat: 24.535708, lng: 118.286969, value: 52},
            {id: 28, lat: 24.535842, lng: 118.287206, value: 40},
            {id: 29, lat: 24.534691, lng: 118.287222, value: 87},
            {id: 30, lat: 24.536208, lng: 118.288642, value: 45},
            {id: 31, lat: 24.536531, lng: 118.288625, value: 17.3},
            {id: 32, lat: 24.536606, lng: 118.288625, value: 18},
            {id: 33, lat: 24.536611, lng: 118.288639, value: 15.6},
            {id: 34, lat: 24.536553, lng: 118.290025, value: 18.6}
        ];

        let canvas, ctx;
        let plotWidth, plotHeight, margin;

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('plotCanvas');
            ctx = canvas.getContext('2d');
            updateCanvas();
            calculateStatistics();
        };

        function updateCanvas() {
            const [width, height] = document.getElementById('canvasSize').value.split(',').map(Number);
            canvas.width = width;
            canvas.height = height;
            canvas.style.maxWidth = '100%';
            
            margin = Math.min(width, height) * 0.1;
            plotWidth = width - 2 * margin;
            plotHeight = height - 2 * margin;
            
            redrawPlot();
        }

        function redrawPlot() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 设置背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 计算坐标范围
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const minLat = Math.min(...lats);
            const maxLat = Math.max(...lats);
            const minLng = Math.min(...lngs);
            const maxLng = Math.max(...lngs);
            
            // 添加边距
            const latRange = maxLat - minLat;
            const lngRange = maxLng - minLng;
            const padding = Math.max(latRange, lngRange) * 0.1;
            
            const plotMinLat = minLat - padding;
            const plotMaxLat = maxLat + padding;
            const plotMinLng = minLng - padding;
            const plotMaxLng = maxLng + padding;
            
            // 绘制网格
            if (document.getElementById('showGrid').checked) {
                drawGrid(plotMinLat, plotMaxLat, plotMinLng, plotMaxLng);
            }
            
            // 绘制坐标轴
            if (document.getElementById('showAxes').checked) {
                drawAxes(plotMinLat, plotMaxLat, plotMinLng, plotMaxLng);
            }
            
            // 绘制点位
            drawPoints(plotMinLat, plotMaxLat, plotMinLng, plotMaxLng);
            
            // 绘制标题和说明
            drawTitle();
            
            // 更新图例
            updateLegend();
            
            // 更新点大小显示
            document.getElementById('pointSizeValue').textContent = document.getElementById('pointSize').value + 'px';
        }

        function drawGrid(minLat, maxLat, minLng, maxLng) {
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 0.5;
            
            // 垂直网格线
            for (let i = 0; i <= 10; i++) {
                const lng = minLng + (maxLng - minLng) * i / 10;
                const x = margin + (lng - minLng) / (maxLng - minLng) * plotWidth;
                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();
            }
            
            // 水平网格线
            for (let i = 0; i <= 10; i++) {
                const lat = minLat + (maxLat - minLat) * i / 10;
                const y = margin + plotHeight - (lat - minLat) / (maxLat - minLat) * plotHeight;
                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }
        }

        function drawAxes(minLat, maxLat, minLng, maxLng) {
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 2;
            ctx.font = '12px Arial';
            ctx.fillStyle = '#333333';
            
            // X轴
            ctx.beginPath();
            ctx.moveTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.stroke();
            
            // Y轴
            ctx.beginPath();
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.stroke();
            
            // X轴标签
            for (let i = 0; i <= 5; i++) {
                const lng = minLng + (maxLng - minLng) * i / 5;
                const x = margin + (lng - minLng) / (maxLng - minLng) * plotWidth;
                ctx.fillText(lng.toFixed(6), x - 30, margin + plotHeight + 20);
            }
            
            // Y轴标签
            for (let i = 0; i <= 5; i++) {
                const lat = minLat + (maxLat - minLat) * i / 5;
                const y = margin + plotHeight - (lat - minLat) / (maxLat - minLat) * plotHeight;
                ctx.fillText(lat.toFixed(6), 5, y + 4);
            }
            
            // 轴标题
            ctx.font = '14px Arial';
            ctx.fillText('经度 (°)', margin + plotWidth/2 - 30, margin + plotHeight + 50);
            
            ctx.save();
            ctx.translate(20, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('纬度 (°)', -30, 0);
            ctx.restore();
        }

        function drawPoints(minLat, maxLat, minLng, maxLng) {
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const labelStyle = document.getElementById('labelStyle').value;
            const colorScheme = document.getElementById('colorScheme').value;
            
            gpsData.forEach((point, index) => {
                const x = margin + (point.lng - minLng) / (maxLng - minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - minLat) / (maxLat - minLat) * plotHeight;
                
                // 获取颜色
                const color = getPointColor(point, index, colorScheme);
                
                // 绘制点
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
                ctx.fill();
                
                // 绘制边框
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 绘制标签
                if (labelStyle !== 'none') {
                    ctx.fillStyle = '#000000';
                    ctx.font = 'bold 11px Arial';
                    
                    let label = '';
                    if (labelStyle === 'numbers') label = point.id.toString();
                    else if (labelStyle === 'values') label = point.value.toString();
                    else if (labelStyle === 'both') label = `${point.id}:${point.value}`;
                    
                    const textWidth = ctx.measureText(label).width;
                    ctx.fillText(label, x - textWidth/2, y - pointSize - 5);
                }
            });
        }

        function getPointColor(point, index, scheme) {
            switch (scheme) {
                case 'value':
                    const minVal = Math.min(...gpsData.map(p => p.value));
                    const maxVal = Math.max(...gpsData.map(p => p.value));
                    const ratio = (point.value - minVal) / (maxVal - minVal);
                    return `hsl(${240 - ratio * 240}, 70%, 50%)`; // 蓝到红
                
                case 'position':
                    const hue = (index / gpsData.length) * 360;
                    return `hsl(${hue}, 70%, 50%)`;
                
                case 'gradient':
                    const lats = gpsData.map(p => p.lat);
                    const minLat = Math.min(...lats);
                    const maxLat = Math.max(...lats);
                    const latRatio = (point.lat - minLat) / (maxLat - minLat);
                    return `hsl(${120 + latRatio * 240}, 70%, 50%)`; // 绿到蓝
                
                default: // single
                    return '#e74c3c';
            }
        }

        function drawTitle() {
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('GPS采样点位分布细节图', canvas.width/2, 30);
            
            ctx.font = '12px Arial';
            ctx.fillText(`总计 ${gpsData.length} 个采样点`, canvas.width/2, 50);
            ctx.textAlign = 'left';
        }

        function updateLegend() {
            const colorScheme = document.getElementById('colorScheme').value;
            const legendDiv = document.getElementById('colorLegend');
            legendDiv.innerHTML = '';
            
            if (colorScheme === 'value') {
                const minVal = Math.min(...gpsData.map(p => p.value));
                const maxVal = Math.max(...gpsData.map(p => p.value));
                const ranges = [
                    {min: minVal, max: minVal + (maxVal - minVal) * 0.25, color: 'hsl(240, 70%, 50%)', label: '低值'},
                    {min: minVal + (maxVal - minVal) * 0.25, max: minVal + (maxVal - minVal) * 0.75, color: 'hsl(120, 70%, 50%)', label: '中值'},
                    {min: minVal + (maxVal - minVal) * 0.75, max: maxVal, color: 'hsl(0, 70%, 50%)', label: '高值'}
                ];
                
                ranges.forEach(range => {
                    const item = document.createElement('div');
                    item.className = 'legend-item';
                    item.innerHTML = `
                        <div class="legend-color" style="background-color: ${range.color};"></div>
                        <span>${range.label} (${range.min.toFixed(1)}-${range.max.toFixed(1)})</span>
                    `;
                    legendDiv.appendChild(item);
                });
            } else {
                const item = document.createElement('div');
                item.className = 'legend-item';
                item.innerHTML = `
                    <div class="legend-color" style="background-color: #e74c3c;"></div>
                    <span>GPS采样点</span>
                `;
                legendDiv.appendChild(item);
            }
        }

        function calculateStatistics() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const values = gpsData.map(p => p.value);
            
            const latSpan = (Math.max(...lats) - Math.min(...lats)) * 111000; // 转换为米
            const lngSpan = (Math.max(...lngs) - Math.min(...lngs)) * 111000 * Math.cos(Math.min(...lats) * Math.PI / 180);
            
            // 计算距离
            let totalDistance = 0;
            let maxDistance = 0;
            let distanceCount = 0;
            
            for (let i = 0; i < gpsData.length; i++) {
                for (let j = i + 1; j < gpsData.length; j++) {
                    const dist = calculateDistance(gpsData[i], gpsData[j]);
                    totalDistance += dist;
                    maxDistance = Math.max(maxDistance, dist);
                    distanceCount++;
                }
            }
            
            document.getElementById('lngSpan').textContent = lngSpan.toFixed(1) + ' m';
            document.getElementById('latSpan').textContent = latSpan.toFixed(1) + ' m';
            document.getElementById('maxDistance').textContent = maxDistance.toFixed(1) + ' m';
            document.getElementById('avgDistance').textContent = (totalDistance / distanceCount).toFixed(1) + ' m';
            document.getElementById('valueRange').textContent = `${Math.min(...values)} - ${Math.max(...values)}`;
        }

        function calculateDistance(p1, p2) {
            const R = 6371000; // 地球半径（米）
            const φ1 = p1.lat * Math.PI / 180;
            const φ2 = p2.lat * Math.PI / 180;
            const Δφ = (p2.lat - p1.lat) * Math.PI / 180;
            const Δλ = (p2.lng - p1.lng) * Math.PI / 180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }

        function downloadPlot() {
            const link = document.createElement('a');
            link.download = 'gps_detail_plot_' + new Date().toISOString().slice(0,10) + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadData() {
            let csv = 'ID,纬度,经度,数值,X坐标,Y坐标\n';
            
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const minLat = Math.min(...lats);
            const maxLat = Math.max(...lats);
            const minLng = Math.min(...lngs);
            const maxLng = Math.max(...lngs);
            
            gpsData.forEach(point => {
                const x = (point.lng - minLng) / (maxLng - minLng) * plotWidth;
                const y = plotHeight - (point.lat - minLat) / (maxLat - minLat) * plotHeight;
                csv += `${point.id},${point.lat},${point.lng},${point.value},${x.toFixed(2)},${y.toFixed(2)}\n`;
            });
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gps_detail_data.csv';
            a.click();
        }
    </script>
</body>
</html>
