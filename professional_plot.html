<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业GPS点位分析图生成器</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 0;
            min-height: 800px;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .control-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .control-section h3 {
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
            font-size: 0.9em;
        }

        .control-group select,
        .control-group input[type="range"],
        .control-group input[type="color"] {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 5px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .btn {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .plot-area {
            padding: 30px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        #plotCanvas {
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            background: white;
        }

        .stats-panel {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: 700;
            color: #155724;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            color: #155724;
            opacity: 0.8;
            margin-top: 4px;
        }

        .legend-panel {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #ffc107;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
            font-size: 0.85em;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .export-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid #17a2b8;
            font-size: 0.9em;
            color: #0c5460;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 专业GPS点位分析系统</h1>
            <p>高精度科研数据可视化与空间分析平台</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="control-section">
                    <h3>🎨 视觉设置</h3>
                    <div class="control-group">
                        <label>画布尺寸</label>
                        <select id="canvasSize" onchange="updateCanvas()">
                            <option value="1000,800">1000×800 (标准)</option>
                            <option value="1200,900">1200×900 (推荐)</option>
                            <option value="1600,1200">1600×1200 (高清)</option>
                            <option value="2000,1500">2000×1500 (超高清)</option>
                            <option value="2400,1800">2400×1800 (印刷级)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>配色方案</label>
                        <select id="colorScheme" onchange="redrawPlot()">
                            <option value="scientific">科学配色</option>
                            <option value="viridis">Viridis (经典)</option>
                            <option value="plasma">Plasma (鲜艳)</option>
                            <option value="cool">冷色调</option>
                            <option value="warm">暖色调</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>点位大小</label>
                        <input type="range" id="pointSize" min="4" max="20" value="10" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>4px</span>
                            <span id="pointSizeValue">10px</span>
                            <span>20px</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>透明度</label>
                        <input type="range" id="opacity" min="0.3" max="1" step="0.1" value="0.9" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>30%</span>
                            <span id="opacityValue">90%</span>
                            <span>100%</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>📊 标注选项</h3>
                    <div class="control-group">
                        <label>标注内容</label>
                        <select id="labelStyle" onchange="redrawPlot()">
                            <option value="id">点位编号</option>
                            <option value="value">测量数值</option>
                            <option value="both">编号+数值</option>
                            <option value="coordinates">坐标信息</option>
                            <option value="none">无标注</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showGrid" checked onchange="redrawPlot()">
                            <label>显示网格</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showAxes" checked onchange="redrawPlot()">
                            <label>显示坐标轴</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showScale" checked onchange="redrawPlot()">
                            <label>显示比例尺</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showConnections" onchange="redrawPlot()">
                            <label>连接线</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showVoronoi" onchange="redrawPlot()">
                            <label>Voronoi图</label>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>📈 分析工具</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showDensity" onchange="redrawPlot()">
                            <label>密度分析</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showClusters" onchange="redrawPlot()">
                            <label>聚类分析</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showTrend" onchange="redrawPlot()">
                            <label>趋势分析</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showContour" onchange="redrawPlot()">
                            <label>等值线</label>
                        </div>
                    </div>
                </div>

                <div class="stats-panel">
                    <h3>📊 统计信息</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value" id="totalPoints">34</span>
                            <div class="stat-label">总点位数</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="areaSize">-</span>
                            <div class="stat-label">覆盖面积</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="avgDistance">-</span>
                            <div class="stat-label">平均间距</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="density">-</span>
                            <div class="stat-label">点位密度</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="valueRange">-</span>
                            <div class="stat-label">数值范围</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="stdDev">-</span>
                            <div class="stat-label">标准差</div>
                        </div>
                    </div>
                </div>

                <div class="legend-panel">
                    <h3>🎨 图例</h3>
                    <div class="legend-grid" id="legendContainer">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <button class="btn btn-primary" onclick="generateHighQualityPlot()">
                    🖼️ 生成专业图表
                </button>

                <button class="btn btn-secondary" onclick="exportData()">
                    📊 导出分析数据
                </button>

                <div class="export-info">
                    <strong>💡 导出说明:</strong><br>
                    生成的图表为PNG格式，分辨率可达2400×1800，适合学术论文发表和高质量印刷。
                </div>
            </div>

            <div class="plot-area">
                <canvas id="plotCanvas"></canvas>
            </div>
        </div>
    </div>

    <div class="loading" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>正在生成高质量图表...</p>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // GPS数据 - 高精度坐标
        const gpsData = [
            {id: 1, lat: 24.538436, lng: 118.290167, value: 15.6, type: 'A'},
            {id: 2, lat: 24.538406, lng: 118.290125, value: 12.3, type: 'A'},
            {id: 3, lat: 24.538378, lng: 118.290100, value: 16.8, type: 'A'},
            {id: 4, lat: 24.538358, lng: 118.290008, value: 17.7, type: 'A'},
            {id: 5, lat: 24.538422, lng: 118.289967, value: 19.1, type: 'A'},
            {id: 6, lat: 24.538417, lng: 118.288389, value: 47.5, type: 'B'},
            {id: 7, lat: 24.537533, lng: 118.285567, value: 24, type: 'B'},
            {id: 8, lat: 24.536917, lng: 118.288572, value: 17.8, type: 'A'},
            {id: 9, lat: 24.535728, lng: 118.287569, value: 33, type: 'B'},
            {id: 10, lat: 24.535686, lng: 118.287540, value: 29, type: 'B'},
            {id: 11, lat: 24.537228, lng: 118.287403, value: 32, type: 'B'},
            {id: 12, lat: 24.537398, lng: 118.287364, value: 17.6, type: 'A'},
            {id: 13, lat: 24.537440, lng: 118.287373, value: 21.6, type: 'A'},
            {id: 14, lat: 24.534508, lng: 118.287501, value: 34.2, type: 'B'},
            {id: 15, lat: 24.537639, lng: 118.288052, value: 16, type: 'A'},
            {id: 16, lat: 24.533858, lng: 118.287717, value: 60.8, type: 'C'},
            {id: 17, lat: 24.536901, lng: 118.287719, value: 50, type: 'C'},
            {id: 18, lat: 24.540792, lng: 118.287722, value: 20, type: 'A'},
            {id: 19, lat: 24.542373, lng: 118.287917, value: 24, type: 'B'},
            {id: 20, lat: 24.540539, lng: 118.287370, value: 20, type: 'A'},
            {id: 21, lat: 24.537632, lng: 118.287483, value: 40, type: 'B'},
            {id: 22, lat: 24.533858, lng: 118.287258, value: 24.5, type: 'B'},
            {id: 23, lat: 24.538314, lng: 118.287460, value: 19, type: 'A'},
            {id: 24, lat: 24.539204, lng: 118.287461, value: 19.4, type: 'A'},
            {id: 25, lat: 24.533736, lng: 118.287914, value: 60, type: 'C'},
            {id: 26, lat: 24.535308, lng: 118.286932, value: 65, type: 'C'},
            {id: 27, lat: 24.535708, lng: 118.286969, value: 52, type: 'C'},
            {id: 28, lat: 24.535842, lng: 118.287206, value: 40, type: 'B'},
            {id: 29, lat: 24.534691, lng: 118.287222, value: 87, type: 'C'},
            {id: 30, lat: 24.536208, lng: 118.288642, value: 45, type: 'B'},
            {id: 31, lat: 24.536531, lng: 118.288625, value: 17.3, type: 'A'},
            {id: 32, lat: 24.536606, lng: 118.288625, value: 18, type: 'A'},
            {id: 33, lat: 24.536611, lng: 118.288639, value: 15.6, type: 'A'},
            {id: 34, lat: 24.536553, lng: 118.290025, value: 18.6, type: 'A'}
        ];

        let canvas, ctx;
        let plotWidth, plotHeight, margin;
        let currentTransform = { scale: 1, translateX: 0, translateY: 0 };

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('plotCanvas');
            ctx = canvas.getContext('2d');

            // 添加鼠标事件
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('click', handleClick);

            updateCanvas();
            calculateStatistics();
        };

        function updateCanvas() {
            const [width, height] = document.getElementById('canvasSize').value.split(',').map(Number);
            canvas.width = width;
            canvas.height = height;
            canvas.style.maxWidth = '100%';
            canvas.style.maxHeight = '600px';

            margin = Math.min(width, height) * 0.12;
            plotWidth = width - 2 * margin;
            plotHeight = height - 2 * margin;

            redrawPlot();
        }

        function redrawPlot() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 设置高质量渲染
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#fafafa');
            gradient.addColorStop(1, '#f0f0f0');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 计算坐标范围
            const bounds = calculateBounds();

            // 绘制网格
            if (document.getElementById('showGrid').checked) {
                drawAdvancedGrid(bounds);
            }

            // 绘制分析图层
            if (document.getElementById('showDensity').checked) {
                drawDensityMap(bounds);
            }

            if (document.getElementById('showContour').checked) {
                drawContourLines(bounds);
            }

            if (document.getElementById('showVoronoi').checked) {
                drawVoronoiDiagram(bounds);
            }

            // 绘制连接线
            if (document.getElementById('showConnections').checked) {
                drawConnections(bounds);
            }

            // 绘制坐标轴
            if (document.getElementById('showAxes').checked) {
                drawProfessionalAxes(bounds);
            }

            // 绘制数据点
            drawDataPoints(bounds);

            // 绘制比例尺
            if (document.getElementById('showScale').checked) {
                drawScale(bounds);
            }

            // 绘制标题和信息
            drawTitleAndInfo();

            // 更新图例
            updateLegend();

            // 更新控制显示
            updateControlDisplays();
        }

        function calculateBounds() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const minLat = Math.min(...lats);
            const maxLat = Math.max(...lats);
            const minLng = Math.min(...lngs);
            const maxLng = Math.max(...lngs);

            // 添加边距
            const latRange = maxLat - minLat;
            const lngRange = maxLng - minLng;
            const padding = Math.max(latRange, lngRange) * 0.15;

            return {
                minLat: minLat - padding,
                maxLat: maxLat + padding,
                minLng: minLng - padding,
                maxLng: maxLng + padding,
                latRange: latRange,
                lngRange: lngRange
            };
        }

        function drawAdvancedGrid(bounds) {
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.lineWidth = 0.5;

            // 主网格线
            const gridLines = 20;
            for (let i = 0; i <= gridLines; i++) {
                // 垂直线
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / gridLines;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();

                // 水平线
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / gridLines;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }

            // 细网格线
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.lineWidth = 0.25;

            const fineGridLines = 100;
            for (let i = 0; i <= fineGridLines; i++) {
                if (i % 5 !== 0) { // 跳过主网格线位置
                    // 垂直细线
                    const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / fineGridLines;
                    const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                    ctx.beginPath();
                    ctx.moveTo(x, margin);
                    ctx.lineTo(x, margin + plotHeight);
                    ctx.stroke();

                    // 水平细线
                    const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / fineGridLines;
                    const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                    ctx.beginPath();
                    ctx.moveTo(margin, y);
                    ctx.lineTo(margin + plotWidth, y);
                    ctx.stroke();
                }
            }
        }

        function drawProfessionalAxes(bounds) {
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.font = '12px Inter, sans-serif';
            ctx.fillStyle = '#2c3e50';

            // 绘制坐标轴
            ctx.beginPath();
            ctx.moveTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.stroke();

            // X轴标签 (经度)
            const xTicks = 8;
            for (let i = 0; i <= xTicks; i++) {
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / xTicks;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                // 刻度线
                ctx.beginPath();
                ctx.moveTo(x, margin + plotHeight);
                ctx.lineTo(x, margin + plotHeight + 8);
                ctx.stroke();

                // 标签
                const label = lng.toFixed(6) + '°';
                const textWidth = ctx.measureText(label).width;
                ctx.fillText(label, x - textWidth/2, margin + plotHeight + 25);
            }

            // Y轴标签 (纬度)
            const yTicks = 8;
            for (let i = 0; i <= yTicks; i++) {
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / yTicks;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                // 刻度线
                ctx.beginPath();
                ctx.moveTo(margin - 8, y);
                ctx.lineTo(margin, y);
                ctx.stroke();

                // 标签
                const label = lat.toFixed(6) + '°';
                const textWidth = ctx.measureText(label).width;
                ctx.fillText(label, margin - textWidth - 15, y + 4);
            }

            // 轴标题
            ctx.font = 'bold 14px Inter, sans-serif';
            ctx.fillText('经度 (Longitude)', margin + plotWidth/2 - 60, margin + plotHeight + 55);

            ctx.save();
            ctx.translate(25, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('纬度 (Latitude)', -50, 0);
            ctx.restore();
        }

        function drawDataPoints(bounds) {
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const opacity = parseFloat(document.getElementById('opacity').value);
            const labelStyle = document.getElementById('labelStyle').value;
            const colorScheme = document.getElementById('colorScheme').value;

            gpsData.forEach((point, index) => {
                const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                // 获取颜色
                const color = getAdvancedColor(point, index, colorScheme);

                // 绘制阴影
                ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                ctx.shadowBlur = 4;
                ctx.shadowOffsetX = 2;
                ctx.shadowOffsetY = 2;

                // 绘制外圈
                ctx.globalAlpha = opacity;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize + 2, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制内圈
                ctx.shadowColor = 'transparent';
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(x, y, pointSize - 1, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制核心点
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize - 3, 0, 2 * Math.PI);
                ctx.fill();

                ctx.globalAlpha = 1;

                // 绘制标签
                if (labelStyle !== 'none') {
                    drawPointLabel(point, x, y, pointSize, labelStyle);
                }
            });
        }

        function getAdvancedColor(point, index, scheme) {
            const values = gpsData.map(p => p.value);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);

            switch (scheme) {
                case 'scientific':
                    const ratio = (point.value - minVal) / (maxVal - minVal);
                    if (ratio < 0.33) return '#2166ac';      // 深蓝
                    else if (ratio < 0.66) return '#762a83'; // 紫色
                    else return '#c51b7d';                   // 深红

                case 'viridis':
                    const v = (point.value - minVal) / (maxVal - minVal);
                    return `hsl(${240 + v * 60}, ${70 + v * 20}%, ${30 + v * 40}%)`;

                case 'plasma':
                    const p = (point.value - minVal) / (maxVal - minVal);
                    return `hsl(${280 - p * 80}, ${80 + p * 15}%, ${25 + p * 50}%)`;

                case 'cool':
                    const c = (point.value - minVal) / (maxVal - minVal);
                    return `hsl(${200 + c * 40}, 70%, ${40 + c * 30}%)`;

                case 'warm':
                    const w = (point.value - minVal) / (maxVal - minVal);
                    return `hsl(${30 - w * 30}, ${70 + w * 20}%, ${45 + w * 25}%)`;

                default: // custom
                    return '#e74c3c';
            }
        }

        function drawPointLabel(point, x, y, pointSize, labelStyle) {
            ctx.font = 'bold 11px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let label = '';
            switch (labelStyle) {
                case 'id':
                    label = point.id.toString();
                    break;
                case 'value':
                    label = point.value.toString();
                    break;
                case 'both':
                    label = `${point.id}:${point.value}`;
                    break;
                case 'coordinates':
                    label = `${point.lat.toFixed(4)},${point.lng.toFixed(4)}`;
                    break;
            }

            // 绘制标签背景
            const textWidth = ctx.measureText(label).width;
            const textHeight = 14;
            const padding = 4;

            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(
                x - textWidth/2 - padding,
                y - pointSize - textHeight - padding - 5,
                textWidth + padding * 2,
                textHeight + padding
            );

            // 绘制标签边框
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.lineWidth = 1;
            ctx.strokeRect(
                x - textWidth/2 - padding,
                y - pointSize - textHeight - padding - 5,
                textWidth + padding * 2,
                textHeight + padding
            );

            // 绘制标签文字
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(label, x, y - pointSize - 8);
        }

        function drawConnections(bounds) {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.lineWidth = 1;

            // 连接相邻点位
            for (let i = 0; i < gpsData.length - 1; i++) {
                const p1 = gpsData[i];
                const p2 = gpsData[i + 1];

                const x1 = margin + (p1.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y1 = margin + plotHeight - (p1.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;
                const x2 = margin + (p2.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y2 = margin + plotHeight - (p2.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
        }

        function drawScale(bounds) {
            const scaleLength = 100; // 像素
            const realDistance = (bounds.maxLng - bounds.minLng) * (scaleLength / plotWidth) * 111000; // 转换为米

            const x = margin + 20;
            const y = margin + plotHeight - 40;

            // 绘制比例尺
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + scaleLength, y);
            ctx.stroke();

            // 绘制刻度
            ctx.beginPath();
            ctx.moveTo(x, y - 5);
            ctx.lineTo(x, y + 5);
            ctx.moveTo(x + scaleLength, y - 5);
            ctx.lineTo(x + scaleLength, y + 5);
            ctx.stroke();

            // 标签
            ctx.font = 'bold 12px Inter, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText(`${realDistance.toFixed(0)} m`, x + scaleLength/2, y - 15);
        }

        function drawTitleAndInfo() {
            // 主标题
            ctx.font = 'bold 24px Inter, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText('GPS采样点位空间分布分析图', canvas.width/2, 40);

            // 副标题
            ctx.font = '14px Inter, sans-serif';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText(`总计 ${gpsData.length} 个采样点 | 高精度坐标分析`, canvas.width/2, 65);

            // 右上角信息
            ctx.font = '12px Inter, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillStyle = '#95a5a6';
            const date = new Date().toLocaleDateString('zh-CN');
            ctx.fillText(`生成日期: ${date}`, canvas.width - 20, 30);
            ctx.fillText('坐标系统: WGS84', canvas.width - 20, 50);
        }

        function drawDensityMap(bounds) {
            // 简化的密度图实现
            const gridSize = 20;
            const density = new Array(gridSize).fill(0).map(() => new Array(gridSize).fill(0));

            // 计算密度
            gpsData.forEach(point => {
                const gridX = Math.floor((point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * (gridSize - 1));
                const gridY = Math.floor((point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * (gridSize - 1));

                if (gridX >= 0 && gridX < gridSize && gridY >= 0 && gridY < gridSize) {
                    density[gridY][gridX]++;
                }
            });

            // 绘制密度图
            const cellWidth = plotWidth / gridSize;
            const cellHeight = plotHeight / gridSize;

            for (let i = 0; i < gridSize; i++) {
                for (let j = 0; j < gridSize; j++) {
                    if (density[i][j] > 0) {
                        const alpha = Math.min(density[i][j] / 5, 0.5);
                        ctx.fillStyle = `rgba(255, 0, 0, ${alpha})`;
                        ctx.fillRect(
                            margin + j * cellWidth,
                            margin + (gridSize - 1 - i) * cellHeight,
                            cellWidth,
                            cellHeight
                        );
                    }
                }
            }
        }

        function drawVoronoiDiagram(bounds) {
            // 简化的Voronoi图实现
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.2)';
            ctx.lineWidth = 1;

            // 这里可以实现更复杂的Voronoi算法
            // 目前使用简化版本
        }

        function drawContourLines(bounds) {
            // 简化的等值线实现
            ctx.strokeStyle = 'rgba(46, 204, 113, 0.6)';
            ctx.lineWidth = 2;

            // 这里可以实现等值线算法
            // 目前使用简化版本
        }

        function updateLegend() {
            const colorScheme = document.getElementById('colorScheme').value;
            const legendContainer = document.getElementById('legendContainer');
            legendContainer.innerHTML = '';

            const values = gpsData.map(p => p.value);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);

            if (colorScheme === 'scientific') {
                const ranges = [
                    {min: minVal, max: minVal + (maxVal - minVal) * 0.33, color: '#2166ac', label: '低值区'},
                    {min: minVal + (maxVal - minVal) * 0.33, max: minVal + (maxVal - minVal) * 0.66, color: '#762a83', label: '中值区'},
                    {min: minVal + (maxVal - minVal) * 0.66, max: maxVal, color: '#c51b7d', label: '高值区'}
                ];

                ranges.forEach(range => {
                    const item = document.createElement('div');
                    item.className = 'legend-item';
                    item.innerHTML = `
                        <div class="legend-color" style="background-color: ${range.color};"></div>
                        <span>${range.label} (${range.min.toFixed(1)}-${range.max.toFixed(1)})</span>
                    `;
                    legendContainer.appendChild(item);
                });
            } else {
                const item = document.createElement('div');
                item.className = 'legend-item';
                item.innerHTML = `
                    <div class="legend-color" style="background-color: #e74c3c;"></div>
                    <span>GPS采样点 (${minVal}-${maxVal})</span>
                `;
                legendContainer.appendChild(item);
            }
        }

        function updateControlDisplays() {
            document.getElementById('pointSizeValue').textContent = document.getElementById('pointSize').value + 'px';
            document.getElementById('opacityValue').textContent = Math.round(parseFloat(document.getElementById('opacity').value) * 100) + '%';
        }

        function calculateStatistics() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const values = gpsData.map(p => p.value);

            // 计算面积
            const latSpan = (Math.max(...lats) - Math.min(...lats)) * 111000;
            const lngSpan = (Math.max(...lngs) - Math.min(...lngs)) * 111000 * Math.cos(Math.min(...lats) * Math.PI / 180);
            const area = latSpan * lngSpan;

            // 计算平均距离
            let totalDistance = 0;
            let count = 0;
            for (let i = 0; i < gpsData.length; i++) {
                for (let j = i + 1; j < gpsData.length; j++) {
                    totalDistance += calculateDistance(gpsData[i], gpsData[j]);
                    count++;
                }
            }
            const avgDistance = totalDistance / count;

            // 计算标准差
            const mean = values.reduce((a, b) => a + b) / values.length;
            const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
            const stdDev = Math.sqrt(variance);

            // 更新显示
            document.getElementById('areaSize').textContent = area > 1000000 ?
                (area/1000000).toFixed(2) + ' km²' : area.toFixed(0) + ' m²';
            document.getElementById('avgDistance').textContent = avgDistance.toFixed(1) + ' m';
            document.getElementById('density').textContent = (gpsData.length / (area/1000000)).toFixed(1) + ' 点/km²';
            document.getElementById('valueRange').textContent = `${Math.min(...values)}-${Math.max(...values)}`;
            document.getElementById('stdDev').textContent = stdDev.toFixed(2);
        }

        function calculateDistance(p1, p2) {
            const R = 6371000;
            const φ1 = p1.lat * Math.PI / 180;
            const φ2 = p2.lat * Math.PI / 180;
            const Δφ = (p2.lat - p1.lat) * Math.PI / 180;
            const Δλ = (p2.lng - p1.lng) * Math.PI / 180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }

        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 检查是否悬停在点位上
            const bounds = calculateBounds();
            const tooltip = document.getElementById('tooltip');
            let found = false;

            gpsData.forEach(point => {
                const px = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const py = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                const distance = Math.sqrt((x - px) ** 2 + (y - py) ** 2);
                if (distance < 15) {
                    tooltip.style.display = 'block';
                    tooltip.style.left = (event.clientX + 10) + 'px';
                    tooltip.style.top = (event.clientY - 10) + 'px';
                    tooltip.innerHTML = `
                        <strong>点位 ${point.id}</strong><br>
                        数值: ${point.value}<br>
                        坐标: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)}
                    `;
                    found = true;
                }
            });

            if (!found) {
                tooltip.style.display = 'none';
            }
        }

        function handleClick(event) {
            // 点击事件处理
        }

        function generateHighQualityPlot() {
            document.getElementById('loadingOverlay').style.display = 'flex';

            setTimeout(() => {
                const link = document.createElement('a');
                link.download = 'professional_gps_analysis_' + new Date().toISOString().slice(0,10) + '.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1000);
        }

        function exportData() {
            let csv = 'ID,纬度,经度,数值,类型,X坐标,Y坐标\n';

            const bounds = calculateBounds();
            gpsData.forEach(point => {
                const x = (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;
                csv += `${point.id},${point.lat},${point.lng},${point.value},${point.type},${x.toFixed(2)},${y.toFixed(2)}\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gps_analysis_data.csv';
            a.click();
        }
    </script>
</body>
</html>
