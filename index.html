<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度地理坐标距离计算器 - 厘米级精度</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .point-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #4facfe;
        }

        .point-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .point-title::before {
            content: "📍";
            margin-right: 10px;
            font-size: 1.2em;
        }

        .coordinate-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }

        .coordinate-label {
            font-weight: bold;
            min-width: 60px;
            color: #555;
        }

        .dms-inputs {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .dms-group {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .dms-input {
            width: 70px;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 5px;
            text-align: center;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .dms-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .dms-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .direction-select {
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
        }

        .calculate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 30px 0;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .result-section {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 25px;
            border-left: 4px solid #28a745;
            display: none;
        }

        .result-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #155724;
            margin-bottom: 15px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #c3e6cb;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: bold;
            color: #155724;
        }

        .result-value {
            color: #155724;
            font-family: monospace;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .coordinate-row {
                flex-direction: column;
                align-items: flex-start;
            }

            .dms-inputs {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>高精度地理坐标距离计算器</h1>
            <p>输入两个点的经纬度（度分秒格式），使用Vincenty公式计算厘米级精度距离</p>
            <p style="font-size: 0.9em; margin-top: 5px; opacity: 0.8;">🎯 基于WGS84椭球体模型，精度可达厘米级别</p>
        </div>

        <div class="content">
            <!-- 第一个点 -->
            <div class="point-section">
                <div class="point-title">第一个点</div>

                <div class="coordinate-row">
                    <span class="coordinate-label">纬度:</span>
                    <div class="dms-inputs">
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat1-deg" placeholder="0" min="0" max="90">
                            <span class="dms-label">度</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat1-min" placeholder="0" min="0" max="59">
                            <span class="dms-label">分</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat1-sec" placeholder="0" min="0" max="59" step="0.01">
                            <span class="dms-label">秒</span>
                        </div>
                        <select class="direction-select" id="lat1-dir">
                            <option value="N">北 (N)</option>
                            <option value="S">南 (S)</option>
                        </select>
                    </div>
                </div>

                <div class="coordinate-row">
                    <span class="coordinate-label">经度:</span>
                    <div class="dms-inputs">
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng1-deg" placeholder="0" min="0" max="180">
                            <span class="dms-label">度</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng1-min" placeholder="0" min="0" max="59">
                            <span class="dms-label">分</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng1-sec" placeholder="0" min="0" max="59" step="0.01">
                            <span class="dms-label">秒</span>
                        </div>
                        <select class="direction-select" id="lng1-dir">
                            <option value="E">东 (E)</option>
                            <option value="W">西 (W)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 第二个点 -->
            <div class="point-section">
                <div class="point-title">第二个点</div>

                <div class="coordinate-row">
                    <span class="coordinate-label">纬度:</span>
                    <div class="dms-inputs">
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat2-deg" placeholder="0" min="0" max="90">
                            <span class="dms-label">度</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat2-min" placeholder="0" min="0" max="59">
                            <span class="dms-label">分</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lat2-sec" placeholder="0" min="0" max="59" step="0.01">
                            <span class="dms-label">秒</span>
                        </div>
                        <select class="direction-select" id="lat2-dir">
                            <option value="N">北 (N)</option>
                            <option value="S">南 (S)</option>
                        </select>
                    </div>
                </div>

                <div class="coordinate-row">
                    <span class="coordinate-label">经度:</span>
                    <div class="dms-inputs">
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng2-deg" placeholder="0" min="0" max="180">
                            <span class="dms-label">度</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng2-min" placeholder="0" min="0" max="59">
                            <span class="dms-label">分</span>
                        </div>
                        <div class="dms-group">
                            <input type="number" class="dms-input" id="lng2-sec" placeholder="0" min="0" max="59" step="0.01">
                            <span class="dms-label">秒</span>
                        </div>
                        <select class="direction-select" id="lng2-dir">
                            <option value="E">东 (E)</option>
                            <option value="W">西 (W)</option>
                        </select>
                    </div>
                </div>
            </div>

            <div style="display: flex; gap: 15px; margin: 30px 0;">
                <button class="calculate-btn" onclick="calculateDistance()" style="flex: 1; margin: 0;">🧮 计算距离</button>
                <button class="calculate-btn" onclick="fillExampleData()" style="flex: 1; margin: 0; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">📍 填充示例数据</button>
                <button class="calculate-btn" onclick="clearAllInputs()" style="flex: 1; margin: 0; background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">🗑️ 清空输入</button>
            </div>

            <!-- 结果显示区域 -->
            <div class="result-section" id="result-section">
                <div class="result-title">📏 高精度计算结果 (厘米级精度)</div>
                <div class="result-item">
                    <span class="result-label">距离 (米):</span>
                    <span class="result-value" id="distance-meters">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">距离 (厘米):</span>
                    <span class="result-value" id="distance-cm">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">距离 (公里):</span>
                    <span class="result-value" id="distance-km">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">距离 (英里):</span>
                    <span class="result-value" id="distance-miles">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">距离 (海里):</span>
                    <span class="result-value" id="distance-nm">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">第一个点 (十进制):</span>
                    <span class="result-value" id="point1-decimal">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">第二个点 (十进制):</span>
                    <span class="result-value" id="point2-decimal">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">计算方法:</span>
                    <span class="result-value">Vincenty公式 (WGS84椭球体)</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Haversine对比:</span>
                    <span class="result-value" id="haversine-distance">-</span>
                </div>
                <div class="result-item">
                    <span class="result-label">算法差异:</span>
                    <span class="result-value" id="algorithm-diff">-</span>
                </div>
            </div>

            <!-- 调试信息区域 -->
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: monospace; font-size: 12px;">
                <strong>🔍 调试信息（按F12打开浏览器控制台查看详细日志）</strong>
                <div id="debug-info" style="margin-top: 10px; color: #666;">
                    请输入坐标并计算以查看调试信息
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
