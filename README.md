# 地理坐标距离计算器

一个简单易用的网页版地理坐标距离计算工具，支持度分秒格式输入，可以精确计算两个地理位置之间的距离。

## 功能特点

- 🌍 **度分秒输入**: 支持标准的度分秒（DMS）格式输入经纬度
- 📏 **精确计算**: 使用Haversine公式计算球面距离，精度高
- 🔄 **多单位显示**: 同时显示公里、英里、海里三种距离单位
- 📱 **响应式设计**: 支持桌面和移动设备
- ✨ **美观界面**: 现代化的渐变色设计，用户体验友好
- ⚡ **即时计算**: 无需服务器，纯前端计算，速度快

## 使用方法

1. **打开应用**: 在浏览器中打开 `index.html` 文件
2. **输入第一个点的坐标**:
   - 纬度: 输入度、分、秒，选择北(N)或南(S)
   - 经度: 输入度、分、秒，选择东(E)或西(W)
3. **输入第二个点的坐标**: 同上
4. **点击计算**: 点击"🧮 计算距离"按钮
5. **查看结果**: 结果将显示在页面底部，包括:
   - 距离（公里、英里、海里）
   - 两个点的十进制坐标

## 输入格式说明

### 度分秒格式
- **度**: 0-90（纬度）或 0-180（经度）
- **分**: 0-59
- **秒**: 0-59.99（支持小数）
- **方向**: 
  - 纬度: N（北）或 S（南）
  - 经度: E（东）或 W（西）

### 示例坐标
- **北京天安门**: 39°54'26.35"N, 116°23'29.35"E
- **上海外滩**: 31°14'17.35"N, 121°29'15.35"E
- **广州塔**: 23°6'32.55"N, 113°19'6.24"E

## 技术实现

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **算法**: Haversine公式计算球面距离
- **样式**: CSS Grid + Flexbox 响应式布局
- **兼容性**: 支持现代浏览器（Chrome, Firefox, Safari, Edge）

## 计算原理

使用Haversine公式计算地球表面两点间的最短距离（大圆距离）：

```
a = sin²(Δφ/2) + cos φ1 ⋅ cos φ2 ⋅ sin²(Δλ/2)
c = 2 ⋅ atan2( √a, √(1−a) )
d = R ⋅ c
```

其中：
- φ 是纬度
- λ 是经度
- R 是地球半径（约6371公里）
- d 是两点间距离

## 文件结构

```
distance-calculator/
├── index.html          # 主页面文件
├── script.js           # JavaScript计算逻辑
└── README.md           # 说明文档
```

## 使用场景

- 🗺️ 地理测量和导航
- 📊 地理数据分析
- 🎓 地理教学演示
- 🚀 航空航海计算
- 📱 移动应用开发参考

## 注意事项

1. **精度**: 计算结果基于地球为完美球体的假设，实际地球为椭球体，可能存在微小误差
2. **输入验证**: 请确保输入的坐标在有效范围内
3. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 快速开始

1. 下载所有文件到本地目录
2. 双击 `index.html` 文件在浏览器中打开
3. 开始输入坐标并计算距离

## 许可证

MIT License - 可自由使用、修改和分发

---

**开发者**: Augment Agent  
**版本**: 1.0.0  
**更新日期**: 2024年
