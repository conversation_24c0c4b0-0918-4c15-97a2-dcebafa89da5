/**
 * 地理坐标距离计算器
 * 支持度分秒格式输入，使用Haversine公式计算两点间距离
 */

// 地球半径（公里）
const EARTH_RADIUS_KM = 6371;

/**
 * 将度分秒转换为十进制度数
 * @param {number} degrees - 度
 * @param {number} minutes - 分
 * @param {number} seconds - 秒
 * @param {string} direction - 方向 (N/S/E/W)
 * @returns {number} 十进制度数
 */
function dmsToDecimal(degrees, minutes, seconds, direction) {
    degrees = parseFloat(degrees) || 0;
    minutes = parseFloat(minutes) || 0;
    seconds = parseFloat(seconds) || 0;

    let decimal = degrees + (minutes / 60) + (seconds / 3600);

    // 南纬和西经为负值
    if (direction === 'S' || direction === 'W') {
        decimal = -decimal;
    }

    return decimal;
}

/**
 * 将角度转换为弧度
 * @param {number} degrees - 角度
 * @returns {number} 弧度
 */
function toRadians(degrees) {
    return degrees * (Math.PI / 180);
}

/**
 * 使用Haversine公式计算两点间距离
 * @param {number} lat1 - 第一个点的纬度（十进制度数）
 * @param {number} lng1 - 第一个点的经度（十进制度数）
 * @param {number} lat2 - 第二个点的纬度（十进制度数）
 * @param {number} lng2 - 第二个点的经度（十进制度数）
 * @returns {number} 距离（公里）
 */
function haversineDistance(lat1, lng1, lat2, lng2) {
    // 转换为弧度
    const lat1Rad = toRadians(lat1);
    const lng1Rad = toRadians(lng1);
    const lat2Rad = toRadians(lat2);
    const lng2Rad = toRadians(lng2);

    // 计算差值
    const deltaLat = lat2Rad - lat1Rad;
    const deltaLng = lng2Rad - lng1Rad;

    // Haversine公式
    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    // 计算距离
    const distance = EARTH_RADIUS_KM * c;

    return distance;
}

/**
 * 验证输入数据
 * @param {Object} point - 包含度分秒数据的对象
 * @returns {boolean} 是否有效
 */
function validateInput(point) {
    const { latDeg, latMin, latSec, lngDeg, lngMin, lngSec } = point;

    // 检查纬度范围
    if (latDeg < 0 || latDeg > 90) return false;
    if (latMin < 0 || latMin >= 60) return false;
    if (latSec < 0 || latSec >= 60) return false;

    // 检查经度范围
    if (lngDeg < 0 || lngDeg > 180) return false;
    if (lngMin < 0 || lngMin >= 60) return false;
    if (lngSec < 0 || lngSec >= 60) return false;

    return true;
}

/**
 * 获取输入数据
 * @param {number} pointNum - 点编号 (1 或 2)
 * @returns {Object} 包含度分秒数据的对象
 */
function getInputData(pointNum) {
    return {
        latDeg: document.getElementById(`lat${pointNum}-deg`).value,
        latMin: document.getElementById(`lat${pointNum}-min`).value,
        latSec: document.getElementById(`lat${pointNum}-sec`).value,
        latDir: document.getElementById(`lat${pointNum}-dir`).value,
        lngDeg: document.getElementById(`lng${pointNum}-deg`).value,
        lngMin: document.getElementById(`lng${pointNum}-min`).value,
        lngSec: document.getElementById(`lng${pointNum}-sec`).value,
        lngDir: document.getElementById(`lng${pointNum}-dir`).value
    };
}

/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的字符串
 */
function formatNumber(num, decimals = 2) {
    return num.toFixed(decimals);
}

/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
function showError(message) {
    alert(`❌ 错误: ${message}`);
}

/**
 * 主计算函数
 */
function calculateDistance() {
    try {
        // 获取输入数据
        const point1 = getInputData(1);
        const point2 = getInputData(2);

        // 验证输入
        if (!validateInput(point1)) {
            showError('第一个点的坐标输入有误，请检查数值范围');
            return;
        }

        if (!validateInput(point2)) {
            showError('第二个点的坐标输入有误，请检查数值范围');
            return;
        }

        // 转换为十进制度数
        const lat1 = dmsToDecimal(point1.latDeg, point1.latMin, point1.latSec, point1.latDir);
        const lng1 = dmsToDecimal(point1.lngDeg, point1.lngMin, point1.lngSec, point1.lngDir);
        const lat2 = dmsToDecimal(point2.latDeg, point2.latMin, point2.latSec, point2.latDir);
        const lng2 = dmsToDecimal(point2.lngDeg, point2.lngMin, point2.lngSec, point2.lngDir);

        // 计算距离
        const distanceKm = haversineDistance(lat1, lng1, lat2, lng2);
        const distanceMiles = distanceKm * 0.621371; // 转换为英里
        const distanceNauticalMiles = distanceKm * 0.539957; // 转换为海里

        // 显示结果
        document.getElementById('distance-km').textContent = formatNumber(distanceKm) + ' km';
        document.getElementById('distance-miles').textContent = formatNumber(distanceMiles) + ' miles';
        document.getElementById('distance-nm').textContent = formatNumber(distanceNauticalMiles) + ' nm';
        document.getElementById('point1-decimal').textContent = `${formatNumber(lat1, 6)}°, ${formatNumber(lng1, 6)}°`;
        document.getElementById('point2-decimal').textContent = `${formatNumber(lat2, 6)}°, ${formatNumber(lng2, 6)}°`;

        // 显示结果区域
        document.getElementById('result-section').style.display = 'block';

        // 滚动到结果区域
        document.getElementById('result-section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

    } catch (error) {
        console.error('计算错误:', error);
        showError('计算过程中发生错误，请检查输入数据');
    }
}

/**
 * 页面加载完成后的初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 为所有输入框添加回车键监听
    const inputs = document.querySelectorAll('.dms-input');
    inputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateDistance();
            }
        });
    });

    // 添加示例数据按钮功能（可选）
    console.log('地理坐标距离计算器已加载完成');
    console.log('示例: 北京天安门 (39°54\'26.35"N, 116°23\'29.35"E) 到 上海外滩 (31°14\'17.35"N, 121°29\'15.35"E)');
});

/**
 * 填充示例数据（北京到上海）
 */
function fillExampleData() {
    // 北京天安门
    document.getElementById('lat1-deg').value = 39;
    document.getElementById('lat1-min').value = 54;
    document.getElementById('lat1-sec').value = 26.35;
    document.getElementById('lat1-dir').value = 'N';
    document.getElementById('lng1-deg').value = 116;
    document.getElementById('lng1-min').value = 23;
    document.getElementById('lng1-sec').value = 29.35;
    document.getElementById('lng1-dir').value = 'E';

    // 上海外滩
    document.getElementById('lat2-deg').value = 31;
    document.getElementById('lat2-min').value = 14;
    document.getElementById('lat2-sec').value = 17.35;
    document.getElementById('lat2-dir').value = 'N';
    document.getElementById('lng2-deg').value = 121;
    document.getElementById('lng2-min').value = 29;
    document.getElementById('lng2-sec').value = 15.35;
    document.getElementById('lng2-dir').value = 'E';
}

/**
 * 清空所有输入框
 */
function clearAllInputs() {
    // 清空第一个点
    document.getElementById('lat1-deg').value = '';
    document.getElementById('lat1-min').value = '';
    document.getElementById('lat1-sec').value = '';
    document.getElementById('lat1-dir').value = 'N';
    document.getElementById('lng1-deg').value = '';
    document.getElementById('lng1-min').value = '';
    document.getElementById('lng1-sec').value = '';
    document.getElementById('lng1-dir').value = 'E';

    // 清空第二个点
    document.getElementById('lat2-deg').value = '';
    document.getElementById('lat2-min').value = '';
    document.getElementById('lat2-sec').value = '';
    document.getElementById('lat2-dir').value = 'N';
    document.getElementById('lng2-deg').value = '';
    document.getElementById('lng2-min').value = '';
    document.getElementById('lng2-sec').value = '';
    document.getElementById('lng2-dir').value = 'E';

    // 隐藏结果区域
    document.getElementById('result-section').style.display = 'none';
}

// 将函数暴露到全局作用域，以便HTML中的onclick可以调用
window.calculateDistance = calculateDistance;
window.fillExampleData = fillExampleData;
window.clearAllInputs = clearAllInputs;
