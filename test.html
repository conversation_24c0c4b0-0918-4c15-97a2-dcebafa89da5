<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单距离计算测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .input-group { margin: 10px 0; }
        label { display: inline-block; width: 150px; }
        input { padding: 5px; margin: 2px; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 5px; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>简单距离计算测试</h1>
    
    <h3>方法1：直接输入十进制度数</h3>
    <div class="input-group">
        <label>点1纬度:</label><input type="number" id="lat1" step="0.000001" placeholder="例: 39.907469">
        <label>点1经度:</label><input type="number" id="lng1" step="0.000001" placeholder="例: 116.391248">
    </div>
    <div class="input-group">
        <label>点2纬度:</label><input type="number" id="lat2" step="0.000001" placeholder="例: 39.907479">
        <label>点2经度:</label><input type="number" id="lng2" step="0.000001" placeholder="例: 116.391258">
    </div>
    <button onclick="calculateSimple()">计算距离</button>
    
    <h3>方法2：度分秒输入</h3>
    <div class="input-group">
        <label>点1:</label>
        <input type="number" id="dms1-deg" placeholder="度" style="width:60px">°
        <input type="number" id="dms1-min" placeholder="分" style="width:60px">'
        <input type="number" id="dms1-sec" placeholder="秒" style="width:80px" step="0.01">"
        <select id="dms1-lat-dir"><option value="N">N</option><option value="S">S</option></select>
        <input type="number" id="dms1-lng-deg" placeholder="度" style="width:60px">°
        <input type="number" id="dms1-lng-min" placeholder="分" style="width:60px">'
        <input type="number" id="dms1-lng-sec" placeholder="秒" style="width:80px" step="0.01">"
        <select id="dms1-lng-dir"><option value="E">E</option><option value="W">W</option></select>
    </div>
    <div class="input-group">
        <label>点2:</label>
        <input type="number" id="dms2-deg" placeholder="度" style="width:60px">°
        <input type="number" id="dms2-min" placeholder="分" style="width:60px">'
        <input type="number" id="dms2-sec" placeholder="秒" style="width:80px" step="0.01">"
        <select id="dms2-lat-dir"><option value="N">N</option><option value="S">S</option></select>
        <input type="number" id="dms2-lng-deg" placeholder="度" style="width:60px">°
        <input type="number" id="dms2-lng-min" placeholder="分" style="width:60px">'
        <input type="number" id="dms2-lng-sec" placeholder="秒" style="width:80px" step="0.01">"
        <select id="dms2-lng-dir"><option value="E">E</option><option value="W">W</option></select>
    </div>
    <button onclick="calculateDMS()">计算距离</button>
    
    <div class="result" id="result" style="display:none;">
        <h3>计算结果</h3>
        <div id="output"></div>
    </div>

    <script>
        function toRadians(degrees) {
            return degrees * (Math.PI / 180);
        }

        function dmsToDecimal(degrees, minutes, seconds, direction) {
            degrees = parseFloat(degrees) || 0;
            minutes = parseFloat(minutes) || 0;
            seconds = parseFloat(seconds) || 0;
            
            let decimal = degrees + (minutes / 60) + (seconds / 3600);
            
            if (direction === 'S' || direction === 'W') {
                decimal = -decimal;
            }
            
            return decimal;
        }

        function haversineDistance(lat1, lng1, lat2, lng2) {
            const R = 6371000; // 地球半径（米）
            const φ1 = toRadians(lat1);
            const φ2 = toRadians(lat2);
            const Δφ = toRadians(lat2 - lat1);
            const Δλ = toRadians(lng2 - lng1);

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }

        function calculateSimple() {
            const lat1 = parseFloat(document.getElementById('lat1').value);
            const lng1 = parseFloat(document.getElementById('lng1').value);
            const lat2 = parseFloat(document.getElementById('lat2').value);
            const lng2 = parseFloat(document.getElementById('lng2').value);
            
            if (isNaN(lat1) || isNaN(lng1) || isNaN(lat2) || isNaN(lng2)) {
                alert('请输入有效的坐标');
                return;
            }
            
            const distance = haversineDistance(lat1, lng1, lat2, lng2);
            
            document.getElementById('output').innerHTML = `
                <strong>输入坐标:</strong><br>
                点1: ${lat1.toFixed(8)}°, ${lng1.toFixed(8)}°<br>
                点2: ${lat2.toFixed(8)}°, ${lng2.toFixed(8)}°<br><br>
                <strong>计算结果:</strong><br>
                距离: ${distance.toFixed(3)} 米<br>
                距离: ${(distance/1000).toFixed(6)} 公里<br>
                距离: ${(distance*100).toFixed(1)} 厘米
            `;
            
            document.getElementById('result').style.display = 'block';
            
            console.log('计算详情:', {lat1, lng1, lat2, lng2, distance});
        }

        function calculateDMS() {
            // 获取度分秒输入
            const lat1 = dmsToDecimal(
                document.getElementById('dms1-deg').value,
                document.getElementById('dms1-min').value,
                document.getElementById('dms1-sec').value,
                document.getElementById('dms1-lat-dir').value
            );
            const lng1 = dmsToDecimal(
                document.getElementById('dms1-lng-deg').value,
                document.getElementById('dms1-lng-min').value,
                document.getElementById('dms1-lng-sec').value,
                document.getElementById('dms1-lng-dir').value
            );
            const lat2 = dmsToDecimal(
                document.getElementById('dms2-deg').value,
                document.getElementById('dms2-min').value,
                document.getElementById('dms2-sec').value,
                document.getElementById('dms2-lat-dir').value
            );
            const lng2 = dmsToDecimal(
                document.getElementById('dms2-lng-deg').value,
                document.getElementById('dms2-lng-min').value,
                document.getElementById('dms2-lng-sec').value,
                document.getElementById('dms2-lng-dir').value
            );
            
            const distance = haversineDistance(lat1, lng1, lat2, lng2);
            
            document.getElementById('output').innerHTML = `
                <strong>度分秒输入转换结果:</strong><br>
                点1: ${lat1.toFixed(8)}°, ${lng1.toFixed(8)}°<br>
                点2: ${lat2.toFixed(8)}°, ${lng2.toFixed(8)}°<br><br>
                <strong>计算结果:</strong><br>
                距离: ${distance.toFixed(3)} 米<br>
                距离: ${(distance/1000).toFixed(6)} 公里<br>
                距离: ${(distance*100).toFixed(1)} 厘米
            `;
            
            document.getElementById('result').style.display = 'block';
            
            console.log('DMS计算详情:', {lat1, lng1, lat2, lng2, distance});
        }

        // 填充示例数据
        function fillExample() {
            // 两个很近的点（相距约10米）
            document.getElementById('lat1').value = 39.907469;
            document.getElementById('lng1').value = 116.391248;
            document.getElementById('lat2').value = 39.907479;
            document.getElementById('lng2').value = 116.391258;
        }
        
        // 页面加载时填充示例
        window.onload = fillExample;
    </script>
</body>
</html>
