<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional GPS Point Analysis System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(255,255,255,0.08) 0%, transparent 50%);
        }

        .header h1 {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 0;
            min-height: 900px;
        }

        .sidebar {
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 35px;
            border-right: 2px solid #dee2e6;
        }

        .control-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .control-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        }

        .control-section h3 {
            color: #2c3e50;
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        .control-group {
            margin-bottom: 18px;
        }

        .control-group label {
            display: block;
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .control-group select,
        .control-group input[type="range"],
        .control-group input[type="color"] {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
            background: white;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .checkbox-item:hover {
            background-color: #f8f9fa;
        }

        .checkbox-item input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #667eea;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .btn {
            width: 100%;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-weight: bold;
            font-size: 15px;
            font-family: Arial, sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(40, 167, 69, 0.4);
        }

        .plot-area {
            padding: 40px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        #plotCanvas {
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            border: 3px solid #ffffff;
            background: white;
            transition: transform 0.3s ease;
        }

        #plotCanvas:hover {
            transform: scale(1.02);
        }

        .stats-panel {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #c3e6cb;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.7);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 1.4em;
            font-weight: bold;
            color: #155724;
            display: block;
        }

        .stat-label {
            font-size: 0.85em;
            color: #155724;
            opacity: 0.8;
            margin-top: 5px;
            font-weight: 500;
        }

        .legend-panel {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #ffc107;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1);
        }

        .legend-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .export-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-radius: 15px;
            padding: 20px;
            margin-top: 25px;
            border: 2px solid #17a2b8;
            font-size: 0.95em;
            color: #0c5460;
            font-weight: 500;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            pointer-events: none;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 1400px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                border-right: none;
                border-bottom: 2px solid #dee2e6;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 GPS Spatial Analysis System</h1>
            <p>High-Precision Scientific Data Visualization & Spatial Analysis Platform</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="control-section">
                    <h3>🎨 Visual Settings</h3>
                    <div class="control-group">
                        <label>Canvas Size</label>
                        <select id="canvasSize" onchange="updateCanvas()">
                            <option value="1200,900">1200×900 (Standard)</option>
                            <option value="1600,1200">1600×1200 (High Quality)</option>
                            <option value="2000,1500">2000×1500 (Ultra HD)</option>
                            <option value="2400,1800">2400×1800 (Print Quality)</option>
                            <option value="3000,2250">3000×2250 (Publication)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Color Scheme</label>
                        <select id="colorScheme" onchange="redrawPlot()">
                            <option value="scientific">Scientific (Blue-Purple-Red)</option>
                            <option value="viridis">Viridis (Classic)</option>
                            <option value="plasma">Plasma (Vibrant)</option>
                            <option value="cool">Cool Tones</option>
                            <option value="warm">Warm Tones</option>
                            <option value="elegant">Elegant (Monochrome)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Point Size</label>
                        <input type="range" id="pointSize" min="6" max="25" value="12" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>6px</span>
                            <span id="pointSizeValue">12px</span>
                            <span>25px</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Point Spacing</label>
                        <input type="range" id="spacing" min="1" max="5" step="0.5" value="2" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>Tight</span>
                            <span id="spacingValue">Normal</span>
                            <span>Wide</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>📊 Label Options</h3>
                    <div class="control-group">
                        <label>Label Content</label>
                        <select id="labelStyle" onchange="redrawPlot()">
                            <option value="id">Point ID</option>
                            <option value="value">Measured Value</option>
                            <option value="both">ID + Value</option>
                            <option value="coordinates">Coordinates</option>
                            <option value="none">No Labels</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showGrid" checked onchange="redrawPlot()">
                            <label>Show Grid</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showAxes" checked onchange="redrawPlot()">
                            <label>Show Axes</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showScale" checked onchange="redrawPlot()">
                            <label>Show Scale Bar</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showConnections" onchange="redrawPlot()">
                            <label>Connection Lines</label>
                        </div>
                    </div>
                </div>

                <div class="stats-panel">
                    <h3>📊 Statistical Summary</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value" id="totalPoints">34</span>
                            <div class="stat-label">Total Points</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="areaSize">-</span>
                            <div class="stat-label">Coverage Area</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="avgDistance">-</span>
                            <div class="stat-label">Avg Distance</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="density">-</span>
                            <div class="stat-label">Point Density</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="valueRange">-</span>
                            <div class="stat-label">Value Range</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="stdDev">-</span>
                            <div class="stat-label">Std Deviation</div>
                        </div>
                    </div>
                </div>

                <div class="legend-panel">
                    <h3>🎨 Legend</h3>
                    <div class="legend-grid" id="legendContainer">
                        <!-- Dynamically generated -->
                    </div>
                </div>

                <button class="btn btn-primary" onclick="generateHighQualityPlot()">
                    🖼️ Generate High-Quality Plot
                </button>

                <button class="btn btn-secondary" onclick="exportData()">
                    📊 Export Analysis Data
                </button>

                <div class="export-info">
                    <strong>💡 Export Information:</strong><br>
                    Generated plots are in PNG format with resolutions up to 3000×2250,
                    suitable for academic publication and high-quality printing.
                </div>
            </div>

            <div class="plot-area">
                <canvas id="plotCanvas"></canvas>
            </div>
        </div>
    </div>

    <div class="loading" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold;">Generating High-Quality Plot...</p>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // GPS data with high precision coordinates
        const gpsData = [
            {id: 1, lat: 24.538436, lng: 118.290167, value: 15.6, type: 'A'},
            {id: 2, lat: 24.538406, lng: 118.290125, value: 12.3, type: 'A'},
            {id: 3, lat: 24.538378, lng: 118.290100, value: 16.8, type: 'A'},
            {id: 4, lat: 24.538358, lng: 118.290008, value: 17.7, type: 'A'},
            {id: 5, lat: 24.538422, lng: 118.289967, value: 19.1, type: 'A'},
            {id: 6, lat: 24.538417, lng: 118.288389, value: 47.5, type: 'B'},
            {id: 7, lat: 24.537533, lng: 118.285567, value: 24, type: 'B'},
            {id: 8, lat: 24.536917, lng: 118.288572, value: 17.8, type: 'A'},
            {id: 9, lat: 24.535728, lng: 118.287569, value: 33, type: 'B'},
            {id: 10, lat: 24.535686, lng: 118.287540, value: 29, type: 'B'},
            {id: 11, lat: 24.537228, lng: 118.287403, value: 32, type: 'B'},
            {id: 12, lat: 24.537398, lng: 118.287364, value: 17.6, type: 'A'},
            {id: 13, lat: 24.537440, lng: 118.287373, value: 21.6, type: 'A'},
            {id: 14, lat: 24.534508, lng: 118.287501, value: 34.2, type: 'B'},
            {id: 15, lat: 24.537639, lng: 118.288052, value: 16, type: 'A'},
            {id: 16, lat: 24.533858, lng: 118.287717, value: 60.8, type: 'C'},
            {id: 17, lat: 24.536901, lng: 118.287719, value: 50, type: 'C'},
            {id: 18, lat: 24.540792, lng: 118.287722, value: 20, type: 'A'},
            {id: 19, lat: 24.542373, lng: 118.287917, value: 24, type: 'B'},
            {id: 20, lat: 24.540539, lng: 118.287370, value: 20, type: 'A'},
            {id: 21, lat: 24.537632, lng: 118.287483, value: 40, type: 'B'},
            {id: 22, lat: 24.533858, lng: 118.287258, value: 24.5, type: 'B'},
            {id: 23, lat: 24.538314, lng: 118.287460, value: 19, type: 'A'},
            {id: 24, lat: 24.539204, lng: 118.287461, value: 19.4, type: 'A'},
            {id: 25, lat: 24.533736, lng: 118.287914, value: 60, type: 'C'},
            {id: 26, lat: 24.535308, lng: 118.286932, value: 65, type: 'C'},
            {id: 27, lat: 24.535708, lng: 118.286969, value: 52, type: 'C'},
            {id: 28, lat: 24.535842, lng: 118.287206, value: 40, type: 'B'},
            {id: 29, lat: 24.534691, lng: 118.287222, value: 87, type: 'C'},
            {id: 30, lat: 24.536208, lng: 118.288642, value: 45, type: 'B'},
            {id: 31, lat: 24.536531, lng: 118.288625, value: 17.3, type: 'A'},
            {id: 32, lat: 24.536606, lng: 118.288625, value: 18, type: 'A'},
            {id: 33, lat: 24.536611, lng: 118.288639, value: 15.6, type: 'A'},
            {id: 34, lat: 24.536553, lng: 118.290025, value: 18.6, type: 'A'}
        ];

        let canvas, ctx;
        let plotWidth, plotHeight, margin;
        let adjustedPositions = []; // Store adjusted positions to avoid overlap

        // Initialize
        window.onload = function() {
            canvas = document.getElementById('plotCanvas');
            ctx = canvas.getContext('2d');

            // Add mouse events
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('click', handleClick);

            updateCanvas();
            calculateStatistics();
        };

        function updateCanvas() {
            const [width, height] = document.getElementById('canvasSize').value.split(',').map(Number);
            canvas.width = width;
            canvas.height = height;
            canvas.style.maxWidth = '100%';
            canvas.style.maxHeight = '700px';

            margin = Math.min(width, height) * 0.15;
            plotWidth = width - 2 * margin;
            plotHeight = height - 2 * margin;

            redrawPlot();
        }

        function redrawPlot() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Set high quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // Draw beautiful background
            drawBeautifulBackground();

            // Calculate coordinate bounds
            const bounds = calculateBounds();

            // Calculate adjusted positions to avoid overlap
            calculateAdjustedPositions(bounds);

            // Draw grid
            if (document.getElementById('showGrid').checked) {
                drawElegantGrid(bounds);
            }

            // Draw axes
            if (document.getElementById('showAxes').checked) {
                drawBeautifulAxes(bounds);
            }

            // Draw connection lines
            if (document.getElementById('showConnections').checked) {
                drawConnections(bounds);
            }

            // Draw data points
            drawBeautifulDataPoints(bounds);

            // Draw scale bar
            if (document.getElementById('showScale').checked) {
                drawElegantScale(bounds);
            }

            // Draw title and info
            drawTitleAndInfo();

            // Update legend
            updateLegend();

            // Update control displays
            updateControlDisplays();
        }

        function drawBeautifulBackground() {
            // Create elegant gradient background
            const gradient = ctx.createRadialGradient(
                canvas.width/2, canvas.height/2, 0,
                canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2
            );
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(0.7, '#fafbfc');
            gradient.addColorStop(1, '#f1f3f4');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add subtle texture
            ctx.globalAlpha = 0.03;
            for (let i = 0; i < 100; i++) {
                ctx.fillStyle = '#000000';
                ctx.fillRect(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    1, 1
                );
            }
            ctx.globalAlpha = 1;
        }

        function calculateBounds() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const minLat = Math.min(...lats);
            const maxLat = Math.max(...lats);
            const minLng = Math.min(...lngs);
            const maxLng = Math.max(...lngs);

            // Add padding
            const latRange = maxLat - minLat;
            const lngRange = maxLng - minLng;
            const padding = Math.max(latRange, lngRange) * 0.2;

            return {
                minLat: minLat - padding,
                maxLat: maxLat + padding,
                minLng: minLng - padding,
                maxLng: maxLng + padding,
                latRange: latRange,
                lngRange: lngRange
            };
        }

        function calculateAdjustedPositions(bounds) {
            const spacing = parseFloat(document.getElementById('spacing').value);
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const minDistance = pointSize * spacing * 2;

            adjustedPositions = [];

            // First pass: calculate original positions
            gpsData.forEach((point, index) => {
                const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                adjustedPositions.push({
                    originalX: x,
                    originalY: y,
                    x: x,
                    y: y,
                    point: point,
                    index: index
                });
            });

            // Second pass: adjust positions to avoid overlap
            for (let iterations = 0; iterations < 50; iterations++) {
                let hasOverlap = false;

                for (let i = 0; i < adjustedPositions.length; i++) {
                    for (let j = i + 1; j < adjustedPositions.length; j++) {
                        const pos1 = adjustedPositions[i];
                        const pos2 = adjustedPositions[j];

                        const dx = pos2.x - pos1.x;
                        const dy = pos2.y - pos1.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < minDistance && distance > 0) {
                            hasOverlap = true;

                            // Calculate repulsion force
                            const force = (minDistance - distance) / distance * 0.5;
                            const forceX = dx * force;
                            const forceY = dy * force;

                            // Apply force (move points apart)
                            pos1.x -= forceX;
                            pos1.y -= forceY;
                            pos2.x += forceX;
                            pos2.y += forceY;

                            // Keep points within plot area
                            pos1.x = Math.max(margin + pointSize, Math.min(margin + plotWidth - pointSize, pos1.x));
                            pos1.y = Math.max(margin + pointSize, Math.min(margin + plotHeight - pointSize, pos1.y));
                            pos2.x = Math.max(margin + pointSize, Math.min(margin + plotWidth - pointSize, pos2.x));
                            pos2.y = Math.max(margin + pointSize, Math.min(margin + plotHeight - pointSize, pos2.y));
                        }
                    }
                }

                if (!hasOverlap) break;
            }
        }

        function drawElegantGrid(bounds) {
            // Major grid lines
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.08)';
            ctx.lineWidth = 1;

            const majorGridLines = 12;
            for (let i = 0; i <= majorGridLines; i++) {
                // Vertical lines
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / majorGridLines;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();

                // Horizontal lines
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / majorGridLines;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }

            // Minor grid lines
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.03)';
            ctx.lineWidth = 0.5;

            const minorGridLines = 60;
            for (let i = 0; i <= minorGridLines; i++) {
                if (i % 5 !== 0) { // Skip major grid positions
                    // Vertical minor lines
                    const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / minorGridLines;
                    const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                    ctx.beginPath();
                    ctx.moveTo(x, margin);
                    ctx.lineTo(x, margin + plotHeight);
                    ctx.stroke();

                    // Horizontal minor lines
                    const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / minorGridLines;
                    const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                    ctx.beginPath();
                    ctx.moveTo(margin, y);
                    ctx.lineTo(margin + plotWidth, y);
                    ctx.stroke();
                }
            }
        }

        function drawBeautifulAxes(bounds) {
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.font = 'bold 14px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';

            // Draw main axes with rounded ends
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.stroke();

            // X-axis labels (Longitude)
            const xTicks = 6;
            for (let i = 0; i <= xTicks; i++) {
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / xTicks;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                // Tick marks
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, margin + plotHeight);
                ctx.lineTo(x, margin + plotHeight + 10);
                ctx.stroke();

                // Labels
                ctx.font = '12px Arial, sans-serif';
                const label = lng.toFixed(6) + '°';
                const textWidth = ctx.measureText(label).width;
                ctx.fillText(label, x - textWidth/2, margin + plotHeight + 30);
            }

            // Y-axis labels (Latitude)
            const yTicks = 6;
            for (let i = 0; i <= yTicks; i++) {
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / yTicks;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                // Tick marks
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(margin - 10, y);
                ctx.lineTo(margin, y);
                ctx.stroke();

                // Labels
                ctx.font = '12px Arial, sans-serif';
                const label = lat.toFixed(6) + '°';
                const textWidth = ctx.measureText(label).width;
                ctx.fillText(label, margin - textWidth - 20, y + 4);
            }

            // Axis titles
            ctx.font = 'bold 16px Arial, sans-serif';
            ctx.fillText('Longitude (°)', margin + plotWidth/2 - 50, margin + plotHeight + 65);

            ctx.save();
            ctx.translate(30, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('Latitude (°)', -40, 0);
            ctx.restore();
        }

        function drawBeautifulDataPoints(bounds) {
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const labelStyle = document.getElementById('labelStyle').value;
            const colorScheme = document.getElementById('colorScheme').value;

            adjustedPositions.forEach((pos, index) => {
                const point = pos.point;
                const x = pos.x;
                const y = pos.y;

                // Get color
                const color = getElegantColor(point, index, colorScheme);

                // Draw connection line to original position (if moved)
                const moved = Math.abs(x - pos.originalX) > 2 || Math.abs(y - pos.originalY) > 2;
                if (moved) {
                    ctx.strokeStyle = 'rgba(150, 150, 150, 0.4)';
                    ctx.lineWidth = 1;
                    ctx.setLineDash([3, 3]);
                    ctx.beginPath();
                    ctx.moveTo(pos.originalX, pos.originalY);
                    ctx.lineTo(x, y);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }

                // Draw beautiful point with multiple layers
                // Outer glow
                ctx.shadowColor = color;
                ctx.shadowBlur = 15;
                ctx.globalAlpha = 0.3;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize + 4, 0, 2 * Math.PI);
                ctx.fill();

                // Reset shadow
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.globalAlpha = 1;

                // Main point
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
                ctx.fill();

                // Inner highlight
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.beginPath();
                ctx.arc(x - pointSize/3, y - pointSize/3, pointSize/3, 0, 2 * Math.PI);
                ctx.fill();

                // Border
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
                ctx.stroke();

                // Draw labels
                if (labelStyle !== 'none') {
                    drawElegantLabel(point, x, y, pointSize, labelStyle);
                }
            });
        }

        function getElegantColor(point, index, scheme) {
            const values = gpsData.map(p => p.value);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);
            const ratio = (point.value - minVal) / (maxVal - minVal);

            switch (scheme) {
                case 'scientific':
                    if (ratio < 0.33) return '#3498db';      // Blue
                    else if (ratio < 0.66) return '#9b59b6'; // Purple
                    else return '#e74c3c';                   // Red

                case 'viridis':
                    const h = 240 + ratio * 60;
                    const s = 70 + ratio * 20;
                    const l = 35 + ratio * 35;
                    return `hsl(${h}, ${s}%, ${l}%)`;

                case 'plasma':
                    const h2 = 280 - ratio * 80;
                    const s2 = 80 + ratio * 15;
                    const l2 = 30 + ratio * 45;
                    return `hsl(${h2}, ${s2}%, ${l2}%)`;

                case 'cool':
                    const h3 = 200 + ratio * 40;
                    return `hsl(${h3}, 70%, ${45 + ratio * 25}%)`;

                case 'warm':
                    const h4 = 30 - ratio * 30;
                    return `hsl(${h4}, ${75 + ratio * 20}%, ${50 + ratio * 20}%)`;

                case 'elegant':
                    const gray = 30 + ratio * 40;
                    return `hsl(220, 15%, ${gray}%)`;

                default:
                    return '#2c3e50';
            }
        }

        function drawElegantLabel(point, x, y, pointSize, labelStyle) {
            ctx.font = 'bold 11px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let label = '';
            switch (labelStyle) {
                case 'id':
                    label = point.id.toString();
                    break;
                case 'value':
                    label = point.value.toString();
                    break;
                case 'both':
                    label = `${point.id}: ${point.value}`;
                    break;
                case 'coordinates':
                    label = `${point.lat.toFixed(4)}, ${point.lng.toFixed(4)}`;
                    break;
            }

            // Calculate label position
            const textWidth = ctx.measureText(label).width;
            const textHeight = 12;
            const padding = 6;
            const labelY = y - pointSize - 15;

            // Draw elegant label background
            const gradient = ctx.createLinearGradient(
                x - textWidth/2 - padding, labelY - padding,
                x + textWidth/2 + padding, labelY + textHeight + padding
            );
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
            gradient.addColorStop(1, 'rgba(248, 249, 250, 0.95)');

            ctx.fillStyle = gradient;
            ctx.fillRect(
                x - textWidth/2 - padding,
                labelY - padding,
                textWidth + padding * 2,
                textHeight + padding * 2
            );

            // Draw label border
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.lineWidth = 1;
            ctx.strokeRect(
                x - textWidth/2 - padding,
                labelY - padding,
                textWidth + padding * 2,
                textHeight + padding * 2
            );

            // Draw label text
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(label, x, labelY + textHeight/2);
        }

        function drawConnections(bounds) {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.2)';
            ctx.lineWidth = 1.5;

            // Connect adjacent points
            for (let i = 0; i < adjustedPositions.length - 1; i++) {
                const pos1 = adjustedPositions[i];
                const pos2 = adjustedPositions[i + 1];

                ctx.beginPath();
                ctx.moveTo(pos1.x, pos1.y);
                ctx.lineTo(pos2.x, pos2.y);
                ctx.stroke();
            }
        }

        function drawElegantScale(bounds) {
            const scaleLength = 120;
            const realDistance = (bounds.maxLng - bounds.minLng) * (scaleLength / plotWidth) * 111000;

            const x = margin + 30;
            const y = margin + plotHeight - 60;

            // Draw scale bar background
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(x - 10, y - 25, scaleLength + 20, 40);

            ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.lineWidth = 1;
            ctx.strokeRect(x - 10, y - 25, scaleLength + 20, 40);

            // Draw scale bar
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + scaleLength, y);
            ctx.stroke();

            // Draw tick marks
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x, y - 8);
            ctx.lineTo(x, y + 8);
            ctx.moveTo(x + scaleLength, y - 8);
            ctx.lineTo(x + scaleLength, y + 8);
            ctx.stroke();

            // Label
            ctx.font = 'bold 13px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText(`${realDistance.toFixed(0)} m`, x + scaleLength/2, y - 15);
        }

        function drawTitleAndInfo() {
            // Main title
            ctx.font = 'bold 28px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText('GPS Sampling Points Spatial Distribution Analysis', canvas.width/2, 50);

            // Subtitle
            ctx.font = '16px Arial, sans-serif';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText(`Total ${gpsData.length} sampling points | High-precision coordinate analysis`, canvas.width/2, 80);

            // Top-right info
            ctx.font = '13px Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillStyle = '#95a5a6';
            const date = new Date().toLocaleDateString('en-US');
            ctx.fillText(`Generated: ${date}`, canvas.width - 30, 35);
            ctx.fillText('Coordinate System: WGS84', canvas.width - 30, 55);
            ctx.fillText('Projection: Web Mercator', canvas.width - 30, 75);
        }

        function updateLegend() {
            const colorScheme = document.getElementById('colorScheme').value;
            const legendContainer = document.getElementById('legendContainer');
            legendContainer.innerHTML = '';

            const values = gpsData.map(p => p.value);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);

            if (colorScheme === 'scientific') {
                const ranges = [
                    {min: minVal, max: minVal + (maxVal - minVal) * 0.33, color: '#3498db', label: 'Low Values'},
                    {min: minVal + (maxVal - minVal) * 0.33, max: minVal + (maxVal - minVal) * 0.66, color: '#9b59b6', label: 'Medium Values'},
                    {min: minVal + (maxVal - minVal) * 0.66, max: maxVal, color: '#e74c3c', label: 'High Values'}
                ];

                ranges.forEach(range => {
                    const item = document.createElement('div');
                    item.className = 'legend-item';
                    item.innerHTML = `
                        <div class="legend-color" style="background-color: ${range.color};"></div>
                        <span>${range.label} (${range.min.toFixed(1)}-${range.max.toFixed(1)})</span>
                    `;
                    legendContainer.appendChild(item);
                });
            } else {
                const item = document.createElement('div');
                item.className = 'legend-item';
                item.innerHTML = `
                    <div class="legend-color" style="background-color: #2c3e50;"></div>
                    <span>GPS Sampling Points (${minVal}-${maxVal})</span>
                `;
                legendContainer.appendChild(item);
            }
        }

        function updateControlDisplays() {
            document.getElementById('pointSizeValue').textContent = document.getElementById('pointSize').value + 'px';

            const spacing = parseFloat(document.getElementById('spacing').value);
            const spacingLabels = ['Tight', 'Close', 'Normal', 'Wide', 'Very Wide'];
            const spacingIndex = Math.round((spacing - 1) * 2);
            document.getElementById('spacingValue').textContent = spacingLabels[spacingIndex] || 'Normal';
        }

        function calculateStatistics() {
            const lats = gpsData.map(p => p.lat);
            const lngs = gpsData.map(p => p.lng);
            const values = gpsData.map(p => p.value);

            // Calculate area
            const latSpan = (Math.max(...lats) - Math.min(...lats)) * 111000;
            const lngSpan = (Math.max(...lngs) - Math.min(...lngs)) * 111000 * Math.cos(Math.min(...lats) * Math.PI / 180);
            const area = latSpan * lngSpan;

            // Calculate average distance
            let totalDistance = 0;
            let count = 0;
            for (let i = 0; i < gpsData.length; i++) {
                for (let j = i + 1; j < gpsData.length; j++) {
                    totalDistance += calculateDistance(gpsData[i], gpsData[j]);
                    count++;
                }
            }
            const avgDistance = totalDistance / count;

            // Calculate standard deviation
            const mean = values.reduce((a, b) => a + b) / values.length;
            const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
            const stdDev = Math.sqrt(variance);

            // Update display
            document.getElementById('areaSize').textContent = area > 1000000 ?
                (area/1000000).toFixed(2) + ' km²' : area.toFixed(0) + ' m²';
            document.getElementById('avgDistance').textContent = avgDistance.toFixed(1) + ' m';
            document.getElementById('density').textContent = (gpsData.length / (area/1000000)).toFixed(1) + ' pts/km²';
            document.getElementById('valueRange').textContent = `${Math.min(...values)}-${Math.max(...values)}`;
            document.getElementById('stdDev').textContent = stdDev.toFixed(2);
        }

        function calculateDistance(p1, p2) {
            const R = 6371000;
            const φ1 = p1.lat * Math.PI / 180;
            const φ2 = p2.lat * Math.PI / 180;
            const Δφ = (p2.lat - p1.lat) * Math.PI / 180;
            const Δλ = (p2.lng - p1.lng) * Math.PI / 180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

            return R * c;
        }

        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left * (canvas.width / rect.width);
            const y = event.clientY - rect.top * (canvas.height / rect.height);

            const tooltip = document.getElementById('tooltip');
            let found = false;

            adjustedPositions.forEach(pos => {
                const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
                if (distance < 20) {
                    tooltip.style.display = 'block';
                    tooltip.style.left = (event.clientX + 15) + 'px';
                    tooltip.style.top = (event.clientY - 15) + 'px';
                    tooltip.innerHTML = `
                        <strong>Point ${pos.point.id}</strong><br>
                        Value: ${pos.point.value}<br>
                        Coordinates: ${pos.point.lat.toFixed(6)}, ${pos.point.lng.toFixed(6)}<br>
                        Type: ${pos.point.type}
                    `;
                    found = true;
                }
            });

            if (!found) {
                tooltip.style.display = 'none';
            }
        }

        function handleClick(event) {
            // Click event handling
        }

        function generateHighQualityPlot() {
            document.getElementById('loadingOverlay').style.display = 'flex';

            setTimeout(() => {
                const link = document.createElement('a');
                link.download = 'gps_spatial_analysis_' + new Date().toISOString().slice(0,10) + '.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1500);
        }

        function exportData() {
            let csv = 'ID,Latitude,Longitude,Value,Type,Adjusted_X,Adjusted_Y,Original_X,Original_Y\n';

            adjustedPositions.forEach(pos => {
                csv += `${pos.point.id},${pos.point.lat},${pos.point.lng},${pos.point.value},${pos.point.type},${pos.x.toFixed(2)},${pos.y.toFixed(2)},${pos.originalX.toFixed(2)},${pos.originalY.toFixed(2)}\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gps_spatial_analysis_data.csv';
            a.click();
        }
    </script>
</body>
</html>
