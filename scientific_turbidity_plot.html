<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scientific Turbidity Distribution Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 300;
        }

        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 0;
            min-height: 800px;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #dee2e6;
        }

        .control-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .control-section h3 {
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            font-weight: bold;
            color: #495057;
            margin-bottom: 6px;
            font-size: 0.9em;
        }

        .control-group select,
        .control-group input[type="range"] {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            background: white;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px;
            border-radius: 6px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #2c3e50;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 6px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .btn {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 14px;
            font-family: Arial, sans-serif;
            cursor: pointer;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 62, 80, 0.4);
        }

        .plot-area {
            padding: 30px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        #plotCanvas {
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid #dee2e6;
            background: white;
        }

        .legend-panel {
            background: linear-gradient(135deg, #e8f4f8 0%, #d1ecf1 100%);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #bee5eb;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 6px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stats-panel {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #155724;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            color: #155724;
            opacity: 0.8;
            margin-top: 4px;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            pointer-events: none;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2c3e50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 Scientific Turbidity Distribution Analysis</h1>
            <p>High-Precision Water Quality Assessment with Spatial Correlation</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="control-section">
                    <h3>📊 Visualization Settings</h3>
                    <div class="control-group">
                        <label>Output Resolution</label>
                        <select id="canvasSize" onchange="updateCanvas()">
                            <option value="1600,1200">1600×1200 (Standard)</option>
                            <option value="2000,1500">2000×1500 (High Quality)</option>
                            <option value="2400,1800">2400×1800 (Publication)</option>
                            <option value="3000,2250">3000×2250 (Ultra HD)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Point Size</label>
                        <input type="range" id="pointSize" min="12" max="35" value="20" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>12px</span>
                            <span id="pointSizeValue">20px</span>
                            <span>35px</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Label Font Size</label>
                        <input type="range" id="fontSize" min="12" max="24" value="16" onchange="redrawPlot()">
                        <div class="range-display">
                            <span>12px</span>
                            <span id="fontSizeValue">16px</span>
                            <span>24px</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Color Scheme</label>
                        <select id="colorScheme" onchange="redrawPlot()">
                            <option value="scientific">Scientific (Blue-Orange-Red)</option>
                            <option value="viridis">Viridis (Research Standard)</option>
                            <option value="plasma">Plasma (High Contrast)</option>
                            <option value="turbo">Turbo (Full Spectrum)</option>
                        </select>
                    </div>
                </div>

                <div class="control-section">
                    <h3>🎯 Display Options</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showGrid" checked onchange="redrawPlot()">
                            <label>Scientific Grid</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showAxes" checked onchange="redrawPlot()">
                            <label>Coordinate Axes</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showScale" checked onchange="redrawPlot()">
                            <label>Scale Bar</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showContours" onchange="redrawPlot()">
                            <label>Turbidity Contours</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showStatistics" checked onchange="redrawPlot()">
                            <label>Statistical Info</label>
                        </div>
                    </div>
                </div>

                <div class="stats-panel">
                    <h3>📈 Statistical Summary</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value" id="totalPoints">34</span>
                            <div class="stat-label">Sample Points</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="meanTurbidity">-</span>
                            <div class="stat-label">Mean (NTU)</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="stdDev">-</span>
                            <div class="stat-label">Std Dev</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="maxTurbidity">-</span>
                            <div class="stat-label">Max (NTU)</div>
                        </div>
                    </div>
                </div>

                <div class="legend-panel">
                    <h3>🎨 Turbidity Scale</h3>
                    <div class="legend-grid" id="legendContainer">
                        <!-- Dynamically generated -->
                    </div>
                </div>

                <button class="btn" onclick="generateScientificPlot()">
                    📊 Generate Scientific Plot
                </button>
            </div>

            <div class="plot-area">
                <canvas id="plotCanvas"></canvas>
            </div>
        </div>
    </div>

    <div class="loading" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold;">Generating Scientific Plot...</p>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Scientific turbidity data
        const turbidityData = [
            {id: 1, value: 15.6, lat: 24 + 32/60 + 18.37/3600, lng: 118 + 17/60 + 24.60/3600},
            {id: 2, value: 12.3, lat: 24 + 32/60 + 18.26/3600, lng: 118 + 17/60 + 24.45/3600},
            {id: 3, value: 16.8, lat: 24 + 32/60 + 18.16/3600, lng: 118 + 17/60 + 24.36/3600},
            {id: 4, value: 17.7, lat: 24 + 32/60 + 18.09/3600, lng: 118 + 17/60 + 24.03/3600},
            {id: 5, value: 19.1, lat: 24 + 32/60 + 18.32/3600, lng: 118 + 17/60 + 23.88/3600},
            {id: 6, value: 47.5, lat: 24 + 32/60 + 18.30/3600, lng: 118 + 17/60 + 18.20/3600},
            {id: 7, value: 24, lat: 24 + 32/60 + 15.20/3600, lng: 118 + 17/60 + 14.04/3600},
            {id: 8, value: 17.8, lat: 24 + 32/60 + 12.90/3600, lng: 118 + 17/60 + 18.86/3600},
            {id: 9, value: 33, lat: 24 + 32/60 + 8.21/3600, lng: 118 + 17/60 + 17.25/3600},
            {id: 10, value: 29, lat: 24 + 32/60 + 10.47/3600, lng: 118 + 17/60 + 15.24/3600},
            {id: 11, value: 32, lat: 24 + 32/60 + 12.42/3600, lng: 118 + 17/60 + 14.45/3600},
            {id: 12, value: 17.6, lat: 24 + 32/60 + 14.04/3600, lng: 118 + 17/60 + 14.51/3600},
            {id: 13, value: 21.6, lat: 24 + 32/60 + 14.83/3600, lng: 118 + 17/60 + 14.54/3600},
            {id: 14, value: 34.2, lat: 24 + 32/60 + 16.27/3600, lng: 118 + 17/60 + 15.04/3600},
            {id: 15, value: 16, lat: 24 + 32/60 + 17.50/3600, lng: 118 + 17/60 + 15.89/3600},
            {id: 16, value: 60.8, lat: 24 + 32/60 + 20.30/3600, lng: 118 + 17/60 + 16.18/3600},
            {id: 17, value: 50, lat: 24 + 32/60 + 21.64/3600, lng: 118 + 17/60 + 16.19/3600},
            {id: 18, value: 20, lat: 24 + 32/60 + 24.50/3600, lng: 118 + 17/60 + 16.36/3600},
            {id: 19, value: 24, lat: 24 + 32/60 + 25.74/3600, lng: 118 + 17/60 + 15.70/3600},
            {id: 20, value: 20, lat: 24 + 32/60 + 24.34/3600, lng: 118 + 17/60 + 14.33/3600},
            {id: 21, value: 40, lat: 24 + 32/60 + 22.75/3600, lng: 118 + 17/60 + 13.94/3600},
            {id: 22, value: 24.5, lat: 24 + 32/60 + 20.30/3600, lng: 118 + 17/60 + 13.30/3600},
            {id: 23, value: 19, lat: 24 + 32/60 + 17.30/3600, lng: 118 + 17/60 + 12.96/3600},
            {id: 24, value: 19.4, lat: 24 + 32/60 + 13.34/3600, lng: 118 + 17/60 + 12.58/3600},
            {id: 25, value: 60, lat: 24 + 32/60 + 20.25/3600, lng: 118 + 17/60 + 17.49/3600},
            {id: 26, value: 65, lat: 24 + 32/60 + 21.11/3600, lng: 118 + 17/60 + 17.15/3600},
            {id: 27, value: 52, lat: 24 + 32/60 + 21.50/3600, lng: 118 + 17/60 + 17.29/3600},
            {id: 28, value: 40, lat: 24 + 32/60 + 21.03/3600, lng: 118 + 17/60 + 17.54/3600},
            {id: 29, value: 87, lat: 24 + 32/60 + 20.86/3600, lng: 118 + 17/60 + 17.40/3600},
            {id: 30, value: 45, lat: 24 + 32/60 + 21.15/3600, lng: 118 + 17/60 + 20.31/3600},
            {id: 31, value: 17.3, lat: 24 + 32/60 + 21.52/3600, lng: 118 + 17/60 + 21.05/3600},
            {id: 32, value: 18, lat: 24 + 32/60 + 21.78/3600, lng: 118 + 17/60 + 21.05/3600},
            {id: 33, value: 15.6, lat: 24 + 32/60 + 21.80/3600, lng: 118 + 17/60 + 21.10/3600},
            {id: 34, value: 18.6, lat: 24 + 32/60 + 21.59/3600, lng: 118 + 17/60 + 24.09/3600}
        ];

        let canvas, ctx;
        let plotWidth, plotHeight, margin;

        // Initialize
        window.onload = function() {
            canvas = document.getElementById('plotCanvas');
            ctx = canvas.getContext('2d');

            canvas.addEventListener('mousemove', handleMouseMove);

            updateCanvas();
            calculateStatistics();
            updateLegend();
        };

        function updateCanvas() {
            const [width, height] = document.getElementById('canvasSize').value.split(',').map(Number);
            canvas.width = width;
            canvas.height = height;
            canvas.style.maxWidth = '100%';
            canvas.style.maxHeight = '700px';

            margin = Math.min(width, height) * 0.15;
            plotWidth = width - 2 * margin;
            plotHeight = height - 2 * margin;

            redrawPlot();
        }

        function calculateStatistics() {
            const values = turbidityData.map(d => d.value);
            const mean = values.reduce((a, b) => a + b, 0) / values.length;
            const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
            const stdDev = Math.sqrt(variance);
            const max = Math.max(...values);

            document.getElementById('totalPoints').textContent = turbidityData.length;
            document.getElementById('meanTurbidity').textContent = mean.toFixed(1);
            document.getElementById('stdDev').textContent = stdDev.toFixed(1);
            document.getElementById('maxTurbidity').textContent = max.toFixed(1);
        }

        function updateLegend() {
            const scheme = document.getElementById('colorScheme').value;
            const container = document.getElementById('legendContainer');

            const ranges = [
                {min: 0, max: 20, label: 'Low (0-20 NTU)'},
                {min: 20, max: 40, label: 'Moderate (20-40 NTU)'},
                {min: 40, max: 60, label: 'High (40-60 NTU)'},
                {min: 60, max: 100, label: 'Very High (60+ NTU)'}
            ];

            container.innerHTML = ranges.map(range => {
                const color = getColorForValue((range.min + range.max) / 2, scheme);
                return `
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: ${color}"></div>
                        <span>${range.label}</span>
                    </div>
                `;
            }).join('');
        }

        function getColorForValue(value, scheme = 'scientific') {
            const maxVal = Math.max(...turbidityData.map(d => d.value));
            const ratio = Math.min(value / maxVal, 1);

            switch (scheme) {
                case 'scientific':
                    if (ratio < 0.25) return '#3498db';      // Blue
                    else if (ratio < 0.5) return '#2ecc71';  // Green
                    else if (ratio < 0.75) return '#f39c12'; // Orange
                    else return '#e74c3c';                   // Red

                case 'viridis':
                    const r = Math.round(68 + ratio * (253 - 68));
                    const g = Math.round(1 + ratio * (231 - 1));
                    const b = Math.round(84 + ratio * (37 - 84));
                    return `rgb(${r}, ${g}, ${b})`;

                case 'plasma':
                    const pr = Math.round(13 + ratio * (240 - 13));
                    const pg = Math.round(8 + ratio * (249 - 8));
                    const pb = Math.round(135 + ratio * (33 - 135));
                    return `rgb(${pr}, ${pg}, ${pb})`;

                case 'turbo':
                    const tr = Math.round(48 + ratio * (122 - 48));
                    const tg = Math.round(18 + ratio * (4 - 18));
                    const tb = Math.round(59 + ratio * (216 - 59));
                    return `rgb(${tr}, ${tg}, ${tb})`;

                default:
                    return '#3498db';
            }
        }

        function redrawPlot() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Calculate bounds
            const lats = turbidityData.map(d => d.lat);
            const lngs = turbidityData.map(d => d.lng);
            const bounds = {
                minLat: Math.min(...lats),
                maxLat: Math.max(...lats),
                minLng: Math.min(...lngs),
                maxLng: Math.max(...lngs)
            };

            // Add padding to bounds
            const latPadding = (bounds.maxLat - bounds.minLat) * 0.05;
            const lngPadding = (bounds.maxLng - bounds.minLng) * 0.05;
            bounds.minLat -= latPadding;
            bounds.maxLat += latPadding;
            bounds.minLng -= lngPadding;
            bounds.maxLng += lngPadding;

            // Draw background
            drawBackground();

            // Draw grid if enabled
            if (document.getElementById('showGrid').checked) {
                drawScientificGrid(bounds);
            }

            // Draw axes if enabled
            if (document.getElementById('showAxes').checked) {
                drawScientificAxes(bounds);
            }

            // Draw contours if enabled
            if (document.getElementById('showContours').checked) {
                drawTurbidityContours(bounds);
            }

            // Draw data points
            drawDataPoints(bounds);

            // Draw scale bar if enabled
            if (document.getElementById('showScale').checked) {
                drawScaleBar(bounds);
            }

            // Draw title and statistics
            drawScientificTitle();

            if (document.getElementById('showStatistics').checked) {
                drawStatisticsBox();
            }

            updateDisplays();
        }

        function drawBackground() {
            // Subtle gradient background
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#fafbfc');
            gradient.addColorStop(1, '#f8f9fa');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function drawScientificGrid(bounds) {
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1;
            ctx.setLineDash([2, 2]);

            // Vertical grid lines
            for (let i = 0; i <= 10; i++) {
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / 10;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();
            }

            // Horizontal grid lines
            for (let i = 0; i <= 10; i++) {
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / 10;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }

            ctx.setLineDash([]);
        }

        function drawScientificAxes(bounds) {
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.font = 'bold 14px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';

            // Draw main axes
            ctx.beginPath();
            ctx.moveTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.stroke();

            // X-axis labels (Longitude)
            const xTicks = 6;
            ctx.textAlign = 'center';
            for (let i = 0; i <= xTicks; i++) {
                const lng = bounds.minLng + (bounds.maxLng - bounds.minLng) * i / xTicks;
                const x = margin + (lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;

                // Tick marks
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, margin + plotHeight);
                ctx.lineTo(x, margin + plotHeight + 12);
                ctx.stroke();

                // Labels
                ctx.font = '12px Arial, sans-serif';
                const label = lng.toFixed(5) + '°E';
                ctx.fillText(label, x, margin + plotHeight + 35);
            }

            // Y-axis labels (Latitude)
            const yTicks = 6;
            ctx.textAlign = 'right';
            for (let i = 0; i <= yTicks; i++) {
                const lat = bounds.minLat + (bounds.maxLat - bounds.minLat) * i / yTicks;
                const y = margin + plotHeight - (lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                // Tick marks
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(margin - 12, y);
                ctx.lineTo(margin, y);
                ctx.stroke();

                // Labels
                ctx.font = '12px Arial, sans-serif';
                const label = lat.toFixed(5) + '°N';
                ctx.fillText(label, margin - 20, y + 5);
            }

            // Axis titles
            ctx.font = 'bold 16px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Longitude (°E)', margin + plotWidth/2, canvas.height - 25);

            ctx.save();
            ctx.translate(35, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.textAlign = 'center';
            ctx.fillText('Latitude (°N)', 0, 0);
            ctx.restore();
        }

        function drawDataPoints(bounds) {
            const pointSize = parseInt(document.getElementById('pointSize').value);
            const fontSize = parseInt(document.getElementById('fontSize').value);
            const colorScheme = document.getElementById('colorScheme').value;

            turbidityData.forEach(point => {
                const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                const color = getColorForValue(point.value, colorScheme);

                // Draw point shadow
                ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                ctx.beginPath();
                ctx.arc(x + 2, y + 2, pointSize/2, 0, 2 * Math.PI);
                ctx.fill();

                // Draw main point
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, pointSize/2, 0, 2 * Math.PI);
                ctx.fill();

                // Draw point border
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Draw inner highlight
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.beginPath();
                ctx.arc(x - pointSize/6, y - pointSize/6, pointSize/4, 0, 2 * Math.PI);
                ctx.fill();

                // Draw label
                drawPointLabel(point, x, y, pointSize, fontSize);
            });
        }

        function drawPointLabel(point, x, y, pointSize, fontSize) {
            ctx.font = `bold ${fontSize}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const label = `${point.value} NTU`;
            const textWidth = ctx.measureText(label).width;
            const textHeight = fontSize;

            // Calculate label position to avoid overlap
            let labelX = x;
            let labelY = y - pointSize/2 - textHeight/2 - 8;

            // Adjust if label would go outside canvas
            if (labelY < margin + textHeight/2) {
                labelY = y + pointSize/2 + textHeight/2 + 8;
            }
            if (labelX - textWidth/2 < margin) {
                labelX = margin + textWidth/2;
            }
            if (labelX + textWidth/2 > margin + plotWidth) {
                labelX = margin + plotWidth - textWidth/2;
            }

            // Draw label background
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(labelX - textWidth/2 - 4, labelY - textHeight/2 - 2, textWidth + 8, textHeight + 4);

            // Draw label border
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.lineWidth = 1;
            ctx.strokeRect(labelX - textWidth/2 - 4, labelY - textHeight/2 - 2, textWidth + 8, textHeight + 4);

            // Draw label text
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(label, labelX, labelY);
        }

        function drawTurbidityContours(bounds) {
            // Simple contour visualization using interpolation
            ctx.strokeStyle = 'rgba(52, 73, 94, 0.3)';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);

            const contourLevels = [20, 40, 60];

            contourLevels.forEach(level => {
                const points = turbidityData.filter(p => Math.abs(p.value - level) < 10);
                if (points.length > 2) {
                    ctx.beginPath();
                    points.forEach((point, i) => {
                        const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                        const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                        if (i === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    });
                    ctx.stroke();
                }
            });

            ctx.setLineDash([]);
        }

        function drawScaleBar(bounds) {
            const scaleX = margin + plotWidth - 150;
            const scaleY = margin + plotHeight - 50;

            // Calculate scale (approximate)
            const latDiff = bounds.maxLat - bounds.minLat;
            const kmPerDegree = 111; // Approximate km per degree latitude
            const totalKm = latDiff * kmPerDegree;
            const scaleKm = Math.round(totalKm / 5);
            const scaleWidth = (scaleKm / totalKm) * plotWidth / 5;

            // Draw scale bar
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(scaleX, scaleY);
            ctx.lineTo(scaleX + scaleWidth, scaleY);
            ctx.stroke();

            // Draw scale ticks
            ctx.beginPath();
            ctx.moveTo(scaleX, scaleY - 5);
            ctx.lineTo(scaleX, scaleY + 5);
            ctx.moveTo(scaleX + scaleWidth, scaleY - 5);
            ctx.lineTo(scaleX + scaleWidth, scaleY + 5);
            ctx.stroke();

            // Draw scale label
            ctx.font = 'bold 12px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText(`${scaleKm} km`, scaleX + scaleWidth/2, scaleY + 20);
        }

        function drawScientificTitle() {
            ctx.font = 'bold 28px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText('Spatial Distribution of Water Turbidity', canvas.width/2, 60);

            ctx.font = '16px Arial, sans-serif';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('Nephelometric Turbidity Units (NTU) - Scientific Analysis', canvas.width/2, 85);

            // Top-right metadata
            ctx.font = '12px Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillStyle = '#95a5a6';
            const date = new Date().toLocaleDateString('en-US');
            ctx.fillText(`Generated: ${date}`, canvas.width - 40, 30);
            ctx.fillText('Coordinate System: WGS84', canvas.width - 40, 50);
            ctx.fillText('Projection: Geographic', canvas.width - 40, 70);
        }

        function drawStatisticsBox() {
            const boxX = margin + 20;
            const boxY = margin + 20;
            const boxWidth = 200;
            const boxHeight = 120;

            // Draw background
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillRect(boxX, boxY, boxWidth, boxHeight);

            // Draw border
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(boxX, boxY, boxWidth, boxHeight);

            // Draw statistics
            ctx.font = 'bold 14px Arial, sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'left';
            ctx.fillText('Statistical Summary', boxX + 10, boxY + 20);

            ctx.font = '12px Arial, sans-serif';
            const values = turbidityData.map(d => d.value);
            const mean = values.reduce((a, b) => a + b, 0) / values.length;
            const min = Math.min(...values);
            const max = Math.max(...values);
            const median = values.sort((a, b) => a - b)[Math.floor(values.length / 2)];

            ctx.fillText(`Sample Size: ${values.length}`, boxX + 10, boxY + 40);
            ctx.fillText(`Mean: ${mean.toFixed(1)} NTU`, boxX + 10, boxY + 55);
            ctx.fillText(`Median: ${median.toFixed(1)} NTU`, boxX + 10, boxY + 70);
            ctx.fillText(`Range: ${min.toFixed(1)} - ${max.toFixed(1)} NTU`, boxX + 10, boxY + 85);
            ctx.fillText(`Max/Min Ratio: ${(max/min).toFixed(1)}`, boxX + 10, boxY + 100);
        }

        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const mouseX = (event.clientX - rect.left) * (canvas.width / rect.width);
            const mouseY = (event.clientY - rect.top) * (canvas.height / rect.height);

            const tooltip = document.getElementById('tooltip');
            let found = false;

            const lats = turbidityData.map(d => d.lat);
            const lngs = turbidityData.map(d => d.lng);
            const bounds = {
                minLat: Math.min(...lats),
                maxLat: Math.max(...lats),
                minLng: Math.min(...lngs),
                maxLng: Math.max(...lngs)
            };

            const latPadding = (bounds.maxLat - bounds.minLat) * 0.05;
            const lngPadding = (bounds.maxLng - bounds.minLng) * 0.05;
            bounds.minLat -= latPadding;
            bounds.maxLat += latPadding;
            bounds.minLng -= lngPadding;
            bounds.maxLng += lngPadding;

            turbidityData.forEach(point => {
                const x = margin + (point.lng - bounds.minLng) / (bounds.maxLng - bounds.minLng) * plotWidth;
                const y = margin + plotHeight - (point.lat - bounds.minLat) / (bounds.maxLat - bounds.minLat) * plotHeight;

                const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));

                if (distance < 25) {
                    tooltip.style.display = 'block';
                    tooltip.style.left = (event.clientX + 15) + 'px';
                    tooltip.style.top = (event.clientY - 15) + 'px';
                    tooltip.innerHTML = `
                        <strong>Sampling Point ${point.id}</strong><br>
                        <strong>Turbidity: ${point.value} NTU</strong><br>
                        Latitude: ${point.lat.toFixed(6)}°N<br>
                        Longitude: ${point.lng.toFixed(6)}°E<br>
                        <em>Water Quality Assessment</em>
                    `;
                    found = true;
                }
            });

            if (!found) {
                tooltip.style.display = 'none';
            }
        }

        function generateScientificPlot() {
            document.getElementById('loadingOverlay').style.display = 'flex';

            setTimeout(() => {
                const link = document.createElement('a');
                link.download = 'scientific_turbidity_analysis_' + new Date().toISOString().slice(0,10) + '.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1000);
        }

        function updateDisplays() {
            document.getElementById('pointSizeValue').textContent = document.getElementById('pointSize').value + 'px';
            document.getElementById('fontSizeValue').textContent = document.getElementById('fontSize').value + 'px';
            updateLegend();
        }
    </script>
</body>
</html>
