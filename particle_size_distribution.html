<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颗粒级配曲线分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            padding: 30px;
        }

        .controls {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .control-group {
            margin-bottom: 25px;
        }

        .control-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 14px;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
        }

        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .data-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }

        .data-table input {
            width: 100%;
            border: none;
            padding: 4px;
            text-align: center;
            font-size: 12px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 20px;
            position: relative;
        }

        #chartCanvas {
            border: 1px solid #ddd;
            border-radius: 10px;
            cursor: crosshair;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .statistics {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border: 2px solid #e9ecef;
        }

        .statistics h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 颗粒级配曲线分析系统</h1>
            <p>Particle Size Distribution Analysis - 土壤颗粒组成专业分析工具</p>
        </div>

        <div class="main-content">
            <div class="controls">
                <div class="control-group">
                    <label>土层名称</label>
                    <input type="text" id="soilName" value="淤泥质砂" placeholder="输入土层名称">
                </div>

                <div class="control-group">
                    <label>图表标题</label>
                    <input type="text" id="chartTitle" value="颗粒级配曲线" placeholder="输入图表标题">
                </div>

                <div class="control-group">
                    <label>曲线样式</label>
                    <select id="curveStyle">
                        <option value="smooth">平滑曲线</option>
                        <option value="linear">直线连接</option>
                        <option value="stepped">阶梯曲线</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>网格密度</label>
                    <select id="gridDensity">
                        <option value="fine">精细网格</option>
                        <option value="medium" selected>中等网格</option>
                        <option value="coarse">粗糙网格</option>
                    </select>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>粒径范围(mm)</th>
                            <th>平均值(%)</th>
                            <th>最大值(%)</th>
                            <th>最小值(%)</th>
                        </tr>
                    </thead>
                    <tbody id="dataTable">
                        <tr>
                            <td>>20.0</td>
                            <td><input type="number" value="22.8" step="0.1"></td>
                            <td><input type="number" value="38.5" step="0.1"></td>
                            <td><input type="number" value="12.0" step="0.1"></td>
                        </tr>
                        <tr>
                            <td>20.0~2.00</td>
                            <td><input type="number" value="36.3" step="0.1"></td>
                            <td><input type="number" value="43.2" step="0.1"></td>
                            <td><input type="number" value="29.3" step="0.1"></td>
                        </tr>
                        <tr>
                            <td>2.00~0.50</td>
                            <td><input type="number" value="14.5" step="0.1"></td>
                            <td><input type="number" value="17.3" step="0.1"></td>
                            <td><input type="number" value="22.2" step="0.1"></td>
                        </tr>
                        <tr>
                            <td>0.50~0.25</td>
                            <td><input type="number" value="4.8" step="0.1"></td>
                            <td><input type="number" value="6.3" step="0.1"></td>
                            <td><input type="number" value="14.3" step="0.1"></td>
                        </tr>
                        <tr>
                            <td>0.25~0.075</td>
                            <td><input type="number" value="21.6" step="0.1"></td>
                            <td><input type="number" value="31.1" step="0.1"></td>
                            <td><input type="number" value="21.3" step="0.1"></td>
                        </tr>
                        <tr>
                            <td><0.075</td>
                            <td><input type="number" value="0" step="0.1"></td>
                            <td><input type="number" value="0" step="0.1"></td>
                            <td><input type="number" value="0" step="0.1"></td>
                        </tr>
                    </tbody>
                </table>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="updateChart()">🔄 更新图表</button>
                    <button class="btn btn-success" onclick="downloadChart()">💾 下载图表</button>
                </div>

                <div class="statistics">
                    <h3>📊 统计参数</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="d10">-</div>
                            <div class="stat-label">D10 (mm)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="d30">-</div>
                            <div class="stat-label">D30 (mm)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="d60">-</div>
                            <div class="stat-label">D60 (mm)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="cu">-</div>
                            <div class="stat-label">不均匀系数 Cu</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="chartCanvas" width="800" height="600"></canvas>
                <div id="tooltip" class="tooltip"></div>
            </div>
        </div>
    </div>

    <script>
        let canvas, ctx;
        let chartData = [];

        // 粒径对应的累计通过率数据点
        const sizeRanges = [
            { size: 20.0, label: '>20.0mm' },
            { size: 2.0, label: '20.0~2.00mm' },
            { size: 0.5, label: '2.00~0.50mm' },
            { size: 0.25, label: '0.50~0.25mm' },
            { size: 0.075, label: '0.25~0.075mm' },
            { size: 0.01, label: '<0.075mm' }
        ];

        window.onload = function() {
            canvas = document.getElementById('chartCanvas');
            ctx = canvas.getContext('2d');
            canvas.addEventListener('mousemove', handleMouseMove);
            updateChart();
        };

        function updateChart() {
            // 获取数据
            const tableRows = document.querySelectorAll('#dataTable tr');
            chartData = [];

            let cumulativePercent = 0;

            // 计算累计通过率
            tableRows.forEach((row, index) => {
                const inputs = row.querySelectorAll('input');
                if (inputs.length >= 3) {
                    const avgPercent = parseFloat(inputs[0].value) || 0;
                    const maxPercent = parseFloat(inputs[1].value) || 0;
                    const minPercent = parseFloat(inputs[2].value) || 0;

                    cumulativePercent += avgPercent;

                    chartData.push({
                        size: sizeRanges[index].size,
                        label: sizeRanges[index].label,
                        percent: avgPercent,
                        cumulative: Math.min(cumulativePercent, 100),
                        maxCumulative: Math.min(cumulativePercent + (maxPercent - avgPercent), 100),
                        minCumulative: Math.max(cumulativePercent - (avgPercent - minPercent), 0)
                    });
                }
            });

            drawChart();
            calculateStatistics();
        }

        function drawChart() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const margin = 80;
            const plotWidth = canvas.width - 2 * margin;
            const plotHeight = canvas.height - 2 * margin;

            // 绘制背景
            drawBackground();

            // 绘制网格
            drawGrid(margin, plotWidth, plotHeight);

            // 绘制坐标轴
            drawAxes(margin, plotWidth, plotHeight);

            // 绘制曲线
            drawCurves(margin, plotWidth, plotHeight);

            // 绘制标题
            drawTitle();

            // 绘制图例
            drawLegend(margin, plotWidth);
        }

        function drawBackground() {
            // 渐变背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#fafbfc');
            gradient.addColorStop(1, '#f8f9fa');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function drawGrid(margin, plotWidth, plotHeight) {
            const gridDensity = document.getElementById('gridDensity').value;
            let xDivisions, yDivisions;

            switch(gridDensity) {
                case 'fine': xDivisions = 20; yDivisions = 20; break;
                case 'medium': xDivisions = 10; yDivisions = 10; break;
                case 'coarse': xDivisions = 5; yDivisions = 5; break;
            }

            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1;
            ctx.setLineDash([2, 2]);

            // 垂直网格线
            for (let i = 0; i <= xDivisions; i++) {
                const x = margin + (plotWidth * i / xDivisions);
                ctx.beginPath();
                ctx.moveTo(x, margin);
                ctx.lineTo(x, margin + plotHeight);
                ctx.stroke();
            }

            // 水平网格线
            for (let i = 0; i <= yDivisions; i++) {
                const y = margin + (plotHeight * i / yDivisions);
                ctx.beginPath();
                ctx.moveTo(margin, y);
                ctx.lineTo(margin + plotWidth, y);
                ctx.stroke();
            }

            ctx.setLineDash([]);
        }

        function drawAxes(margin, plotWidth, plotHeight) {
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;

            // 主坐标轴
            ctx.beginPath();
            ctx.moveTo(margin, margin);
            ctx.lineTo(margin, margin + plotHeight);
            ctx.lineTo(margin + plotWidth, margin + plotHeight);
            ctx.stroke();

            // X轴标签 (粒径 - 对数刻度)
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';

            const sizeLabels = [0.01, 0.075, 0.25, 0.5, 2.0, 20.0];
            sizeLabels.forEach(size => {
                const logSize = Math.log10(size);
                const logMin = Math.log10(0.01);
                const logMax = Math.log10(20.0);
                const x = margin + plotWidth * (logSize - logMin) / (logMax - logMin);

                // 刻度线
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, margin + plotHeight);
                ctx.lineTo(x, margin + plotHeight + 10);
                ctx.stroke();

                // 标签
                ctx.fillText(size.toString(), x, margin + plotHeight + 30);
            });

            // Y轴标签 (通过率)
            ctx.textAlign = 'right';
            for (let i = 0; i <= 10; i++) {
                const percent = i * 10;
                const y = margin + plotHeight - (plotHeight * i / 10);

                // 刻度线
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(margin - 10, y);
                ctx.lineTo(margin, y);
                ctx.stroke();

                // 标签
                ctx.fillText(percent + '%', margin - 15, y + 5);
            }

            // 坐标轴标题
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('粒径 (mm)', margin + plotWidth/2, canvas.height - 20);

            ctx.save();
            ctx.translate(25, margin + plotHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('累计通过率 (%)', 0, 0);
            ctx.restore();
        }

        function drawCurves(margin, plotWidth, plotHeight) {
            const curveStyle = document.getElementById('curveStyle').value;

            // 转换数据点到画布坐标
            const points = chartData.map(d => {
                const logSize = Math.log10(d.size);
                const logMin = Math.log10(0.01);
                const logMax = Math.log10(20.0);
                const x = margin + plotWidth * (logSize - logMin) / (logMax - logMin);
                const y = margin + plotHeight - (plotHeight * d.cumulative / 100);
                return { x, y, data: d };
            });

            // 绘制主曲线
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 4;
            ctx.beginPath();

            points.forEach((point, index) => {
                if (index === 0) {
                    ctx.moveTo(point.x, point.y);
                } else {
                    if (curveStyle === 'smooth') {
                        // 平滑曲线
                        const prevPoint = points[index - 1];
                        const cp1x = prevPoint.x + (point.x - prevPoint.x) * 0.3;
                        const cp1y = prevPoint.y;
                        const cp2x = point.x - (point.x - prevPoint.x) * 0.3;
                        const cp2y = point.y;
                        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, point.x, point.y);
                    } else if (curveStyle === 'stepped') {
                        // 阶梯曲线
                        const prevPoint = points[index - 1];
                        ctx.lineTo(prevPoint.x, point.y);
                        ctx.lineTo(point.x, point.y);
                    } else {
                        // 直线连接
                        ctx.lineTo(point.x, point.y);
                    }
                }
            });
            ctx.stroke();

            // 绘制数据点
            points.forEach(point => {
                // 点的阴影
                ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                ctx.beginPath();
                ctx.arc(point.x + 2, point.y + 2, 6, 0, 2 * Math.PI);
                ctx.fill();

                // 主点
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
                ctx.fill();

                // 点的边框
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 内部高光
                ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
                ctx.beginPath();
                ctx.arc(point.x - 2, point.y - 2, 2, 0, 2 * Math.PI);
                ctx.fill();
            });
        }

        function drawTitle() {
            const title = document.getElementById('chartTitle').value;
            const soilName = document.getElementById('soilName').value;

            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText(title, canvas.width/2, 40);

            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText(`土层类型: ${soilName}`, canvas.width/2, 65);
        }

        function drawLegend(margin, plotWidth) {
            const legendX = margin + plotWidth - 200;
            const legendY = margin + 20;

            // 图例背景
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillRect(legendX, legendY, 180, 80);
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.strokeRect(legendX, legendY, 180, 80);

            // 图例标题
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'left';
            ctx.fillText('图例', legendX + 10, legendY + 20);

            // 主曲线图例
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(legendX + 10, legendY + 35);
            ctx.lineTo(legendX + 40, legendY + 35);
            ctx.stroke();

            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(legendX + 25, legendY + 35, 4, 0, 2 * Math.PI);
            ctx.fill();

            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#2c3e50';
            ctx.fillText('累计通过率曲线', legendX + 50, legendY + 40);

            // 数据点图例
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('• 筛分试验数据点', legendX + 10, legendY + 60);
        }

        function calculateStatistics() {
            // 计算D10, D30, D60等特征粒径
            const d10 = interpolateSize(10);
            const d30 = interpolateSize(30);
            const d60 = interpolateSize(60);

            const cu = d60 / d10; // 不均匀系数

            document.getElementById('d10').textContent = d10.toFixed(3);
            document.getElementById('d30').textContent = d30.toFixed(3);
            document.getElementById('d60').textContent = d60.toFixed(3);
            document.getElementById('cu').textContent = cu.toFixed(2);
        }

        function interpolateSize(targetPercent) {
            // 在累计通过率曲线上插值计算特定通过率对应的粒径
            for (let i = 0; i < chartData.length - 1; i++) {
                const p1 = chartData[i];
                const p2 = chartData[i + 1];

                if (p1.cumulative <= targetPercent && p2.cumulative >= targetPercent) {
                    const ratio = (targetPercent - p1.cumulative) / (p2.cumulative - p1.cumulative);
                    const logSize1 = Math.log10(p1.size);
                    const logSize2 = Math.log10(p2.size);
                    const logSize = logSize1 + ratio * (logSize2 - logSize1);
                    return Math.pow(10, logSize);
                }
            }
            return 0;
        }

        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const mouseX = (event.clientX - rect.left) * (canvas.width / rect.width);
            const mouseY = (event.clientY - rect.top) * (canvas.height / rect.height);

            const tooltip = document.getElementById('tooltip');
            let found = false;

            const margin = 80;
            const plotWidth = canvas.width - 2 * margin;
            const plotHeight = canvas.height - 2 * margin;

            chartData.forEach(d => {
                const logSize = Math.log10(d.size);
                const logMin = Math.log10(0.01);
                const logMax = Math.log10(20.0);
                const x = margin + plotWidth * (logSize - logMin) / (logMax - logMin);
                const y = margin + plotHeight - (plotHeight * d.cumulative / 100);

                const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));

                if (distance < 15) {
                    tooltip.style.display = 'block';
                    tooltip.style.left = (event.clientX + 15) + 'px';
                    tooltip.style.top = (event.clientY - 15) + 'px';
                    tooltip.innerHTML = `
                        <strong>${d.label}</strong><br>
                        粒径: ${d.size} mm<br>
                        含量: ${d.percent.toFixed(1)}%<br>
                        累计通过率: ${d.cumulative.toFixed(1)}%
                    `;
                    found = true;
                }
            });

            if (!found) {
                tooltip.style.display = 'none';
            }
        }

        function downloadChart() {
            const link = document.createElement('a');
            link.download = '颗粒级配曲线_' + new Date().toISOString().slice(0,10) + '.png';
            link.href = canvas.toDataURL('image/png', 1.0);
            link.click();
        }
    </script>
</body>
</html>
