/**
 * 地理坐标距离计算器
 * 支持度分秒格式输入，使用高精度Vincenty公式计算两点间距离
 * 精度可达厘米级别
 */

// WGS84椭球体参数
const WGS84 = {
    a: 6378137.0,           // 长半轴 (米)
    b: 6356752.314245,      // 短半轴 (米)
    f: 1/298.257223563      // 扁率
};

/**
 * 将度分秒转换为十进制度数
 * @param {number} degrees - 度
 * @param {number} minutes - 分
 * @param {number} seconds - 秒
 * @param {string} direction - 方向 (N/S/E/W)
 * @returns {number} 十进制度数
 */
function dmsToDecimal(degrees, minutes, seconds, direction) {
    degrees = parseFloat(degrees) || 0;
    minutes = parseFloat(minutes) || 0;
    seconds = parseFloat(seconds) || 0;

    let decimal = degrees + (minutes / 60) + (seconds / 3600);

    // 南纬和西经为负值
    if (direction === 'S' || direction === 'W') {
        decimal = -decimal;
    }

    return decimal;
}

/**
 * 将角度转换为弧度
 * @param {number} degrees - 角度
 * @returns {number} 弧度
 */
function toRadians(degrees) {
    return degrees * (Math.PI / 180);
}

/**
 * 使用Vincenty公式计算两点间距离（高精度，厘米级别）
 * @param {number} lat1 - 第一个点的纬度（十进制度数）
 * @param {number} lng1 - 第一个点的经度（十进制度数）
 * @param {number} lat2 - 第二个点的纬度（十进制度数）
 * @param {number} lng2 - 第二个点的经度（十进制度数）
 * @returns {number} 距离（米）
 */
function vincentyDistance(lat1, lng1, lat2, lng2) {
    // 调试信息
    console.log('输入坐标:', {lat1, lng1, lat2, lng2});
    // 转换为弧度
    const φ1 = toRadians(lat1);
    const φ2 = toRadians(lat2);
    const Δλ = toRadians(lng2 - lng1);

    const a = WGS84.a;
    const b = WGS84.b;
    const f = WGS84.f;

    const L = Δλ;
    const U1 = Math.atan((1 - f) * Math.tan(φ1));
    const U2 = Math.atan((1 - f) * Math.tan(φ2));
    const sinU1 = Math.sin(U1);
    const cosU1 = Math.cos(U1);
    const sinU2 = Math.sin(U2);
    const cosU2 = Math.cos(U2);

    let λ = L;
    let λPrev;
    let iterLimit = 100;
    let cos2σM, sinσ, cosσ, σ, sinλ, cosλ, sinα, cos2α;

    do {
        sinλ = Math.sin(λ);
        cosλ = Math.cos(λ);
        const sinSqσ = (cosU2 * sinλ) * (cosU2 * sinλ) +
                       (cosU1 * sinU2 - sinU1 * cosU2 * cosλ) *
                       (cosU1 * sinU2 - sinU1 * cosU2 * cosλ);
        sinσ = Math.sqrt(sinSqσ);

        if (sinσ === 0) return 0; // 重合点

        cosσ = sinU1 * sinU2 + cosU1 * cosU2 * cosλ;
        σ = Math.atan2(sinσ, cosσ);
        sinα = cosU1 * cosU2 * sinλ / sinσ;
        cos2α = 1 - sinα * sinα;
        cos2σM = cosσ - 2 * sinU1 * sinU2 / cos2α;

        if (isNaN(cos2σM)) cos2σM = 0; // 赤道线

        const C = f / 16 * cos2α * (4 + f * (4 - 3 * cos2α));
        λPrev = λ;
        λ = L + (1 - C) * f * sinα *
            (σ + C * sinσ * (cos2σM + C * cosσ * (-1 + 2 * cos2σM * cos2σM)));
    } while (Math.abs(λ - λPrev) > 1e-12 && --iterLimit > 0);

    if (iterLimit === 0) {
        throw new Error('Vincenty公式未收敛');
    }

    const uSq = cos2α * (a * a - b * b) / (b * b);
    const A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    const B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
    const Δσ = B * sinσ * (cos2σM + B / 4 * (cosσ * (-1 + 2 * cos2σM * cos2σM) -
                B / 6 * cos2σM * (-3 + 4 * sinσ * sinσ) * (-3 + 4 * cos2σM * cos2σM)));

    const s = b * A * (σ - Δσ);

    console.log('Vincenty计算结果:', s, '米');
    return s; // 返回距离（米）
}

/**
 * 简单的Haversine公式计算（用于对比验证）
 * @param {number} lat1 - 第一个点的纬度（十进制度数）
 * @param {number} lng1 - 第一个点的经度（十进制度数）
 * @param {number} lat2 - 第二个点的纬度（十进制度数）
 * @param {number} lng2 - 第二个点的经度（十进制度数）
 * @returns {number} 距离（米）
 */
function haversineDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000; // 地球半径（米）
    const φ1 = toRadians(lat1);
    const φ2 = toRadians(lat2);
    const Δφ = toRadians(lat2 - lat1);
    const Δλ = toRadians(lng2 - lng1);

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const distance = R * c;
    console.log('Haversine计算结果:', distance, '米');
    return distance;
}

/**
 * 验证输入数据
 * @param {Object} point - 包含度分秒数据的对象
 * @returns {boolean} 是否有效
 */
function validateInput(point) {
    const { latDeg, latMin, latSec, lngDeg, lngMin, lngSec } = point;

    // 检查纬度范围
    if (latDeg < 0 || latDeg > 90) return false;
    if (latMin < 0 || latMin >= 60) return false;
    if (latSec < 0 || latSec >= 60) return false;

    // 检查经度范围
    if (lngDeg < 0 || lngDeg > 180) return false;
    if (lngMin < 0 || lngMin >= 60) return false;
    if (lngSec < 0 || lngSec >= 60) return false;

    return true;
}

/**
 * 获取输入数据
 * @param {number} pointNum - 点编号 (1 或 2)
 * @returns {Object} 包含度分秒数据的对象
 */
function getInputData(pointNum) {
    return {
        latDeg: document.getElementById(`lat${pointNum}-deg`).value,
        latMin: document.getElementById(`lat${pointNum}-min`).value,
        latSec: document.getElementById(`lat${pointNum}-sec`).value,
        latDir: document.getElementById(`lat${pointNum}-dir`).value,
        lngDeg: document.getElementById(`lng${pointNum}-deg`).value,
        lngMin: document.getElementById(`lng${pointNum}-min`).value,
        lngSec: document.getElementById(`lng${pointNum}-sec`).value,
        lngDir: document.getElementById(`lng${pointNum}-dir`).value
    };
}

/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的字符串
 */
function formatNumber(num, decimals = 2) {
    return num.toFixed(decimals);
}

/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
function showError(message) {
    alert(`❌ 错误: ${message}`);
}

/**
 * 主计算函数
 */
function calculateDistance() {
    try {
        // 获取输入数据
        const point1 = getInputData(1);
        const point2 = getInputData(2);

        // 验证输入
        if (!validateInput(point1)) {
            showError('第一个点的坐标输入有误，请检查数值范围');
            return;
        }

        if (!validateInput(point2)) {
            showError('第二个点的坐标输入有误，请检查数值范围');
            return;
        }

        // 转换为十进制度数
        const lat1 = dmsToDecimal(point1.latDeg, point1.latMin, point1.latSec, point1.latDir);
        const lng1 = dmsToDecimal(point1.lngDeg, point1.lngMin, point1.lngSec, point1.lngDir);
        const lat2 = dmsToDecimal(point2.latDeg, point2.latMin, point2.latSec, point2.latDir);
        const lng2 = dmsToDecimal(point2.lngDeg, point2.lngMin, point2.lngSec, point2.lngDir);

        // 计算距离（使用两种算法进行对比）
        console.log('开始计算距离...');
        console.log('十进制坐标:', {lat1, lng1, lat2, lng2});

        const distanceVincenty = vincentyDistance(lat1, lng1, lat2, lng2);
        const distanceHaversine = haversineDistance(lat1, lng1, lat2, lng2);

        // 使用Vincenty结果作为主要结果
        const distanceMeters = distanceVincenty;
        const distanceKm = distanceMeters / 1000;
        const distanceMiles = distanceKm * 0.621371; // 转换为英里
        const distanceNauticalMiles = distanceKm * 0.539957; // 转换为海里
        const distanceCm = distanceMeters * 100; // 转换为厘米

        console.log('算法对比:');
        console.log('- Vincenty:', distanceVincenty, '米');
        console.log('- Haversine:', distanceHaversine, '米');
        console.log('- 差异:', Math.abs(distanceVincenty - distanceHaversine), '米');

        // 显示结果（高精度）
        document.getElementById('distance-meters').textContent = formatNumber(distanceMeters, 3) + ' m';
        document.getElementById('distance-cm').textContent = formatNumber(distanceCm, 1) + ' cm';
        document.getElementById('distance-km').textContent = formatNumber(distanceKm, 6) + ' km';
        document.getElementById('distance-miles').textContent = formatNumber(distanceMiles, 6) + ' miles';
        document.getElementById('distance-nm').textContent = formatNumber(distanceNauticalMiles, 6) + ' nm';
        document.getElementById('point1-decimal').textContent = `${formatNumber(lat1, 8)}°, ${formatNumber(lng1, 8)}°`;
        document.getElementById('point2-decimal').textContent = `${formatNumber(lat2, 8)}°, ${formatNumber(lng2, 8)}°`;

        // 显示结果区域
        document.getElementById('result-section').style.display = 'block';

        // 滚动到结果区域
        document.getElementById('result-section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

    } catch (error) {
        console.error('计算错误:', error);
        showError('计算过程中发生错误，请检查输入数据');
    }
}

/**
 * 页面加载完成后的初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 为所有输入框添加回车键监听
    const inputs = document.querySelectorAll('.dms-input');
    inputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateDistance();
            }
        });
    });

    // 添加示例数据按钮功能（可选）
    console.log('地理坐标距离计算器已加载完成');
    console.log('示例: 北京天安门 (39°54\'26.35"N, 116°23\'29.35"E) 到 上海外滩 (31°14\'17.35"N, 121°29\'15.35"E)');
});

/**
 * 填充示例数据（北京到上海高精度坐标）
 */
function fillExampleData() {
    // 北京天安门（高精度坐标）
    document.getElementById('lat1-deg').value = 39;
    document.getElementById('lat1-min').value = 54;
    document.getElementById('lat1-sec').value = 26.89;
    document.getElementById('lat1-dir').value = 'N';
    document.getElementById('lng1-deg').value = 116;
    document.getElementById('lng1-min').value = 23;
    document.getElementById('lng1-sec').value = 29.17;
    document.getElementById('lng1-dir').value = 'E';

    // 上海外滩（高精度坐标）
    document.getElementById('lat2-deg').value = 31;
    document.getElementById('lat2-min').value = 14;
    document.getElementById('lat2-sec').value = 17.88;
    document.getElementById('lat2-dir').value = 'N';
    document.getElementById('lng2-deg').value = 121;
    document.getElementById('lng2-min').value = 29;
    document.getElementById('lng2-sec').value = 15.12;
    document.getElementById('lng2-dir').value = 'E';
}

/**
 * 清空所有输入框
 */
function clearAllInputs() {
    // 清空第一个点
    document.getElementById('lat1-deg').value = '';
    document.getElementById('lat1-min').value = '';
    document.getElementById('lat1-sec').value = '';
    document.getElementById('lat1-dir').value = 'N';
    document.getElementById('lng1-deg').value = '';
    document.getElementById('lng1-min').value = '';
    document.getElementById('lng1-sec').value = '';
    document.getElementById('lng1-dir').value = 'E';

    // 清空第二个点
    document.getElementById('lat2-deg').value = '';
    document.getElementById('lat2-min').value = '';
    document.getElementById('lat2-sec').value = '';
    document.getElementById('lat2-dir').value = 'N';
    document.getElementById('lng2-deg').value = '';
    document.getElementById('lng2-min').value = '';
    document.getElementById('lng2-sec').value = '';
    document.getElementById('lng2-dir').value = 'E';

    // 隐藏结果区域
    document.getElementById('result-section').style.display = 'none';
}

// 将函数暴露到全局作用域，以便HTML中的onclick可以调用
window.calculateDistance = calculateDistance;
window.fillExampleData = fillExampleData;
window.clearAllInputs = clearAllInputs;
